# Package Documentation: The Otter Group

## Project Overview
- **Name**: vite_react_shadcn_ts
- **Description**: Hockey equipment e-commerce platform with integrated dining experience
- **Tech Stack**: Vite, React, TypeScript, Shadcn UI, Tailwind CSS
- **Type**: Module (ESM)

## Scripts
| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build production-optimized bundle |
| `npm run build:dev` | Build development bundle for testing |
| `npm run lint` | Lint codebase using ESLint |
| `npm run preview` | Preview production build locally |
| `npm run test` | Run unit tests with Vitest |

## Dependencies

### Core Libraries
| Package | Version | Purpose |
|---------|---------|---------|
| react | ^18.3.1 | UI library |
| react-dom | ^18.3.1 | React DOM rendering |
| react-router-dom | ^6.26.2 | Routing |
| @tanstack/react-query | ^5.56.2 | State management |
| zod | ^3.23.8 | Schema validation |
| convex | ^1.12.0 | Backend-as-a-Service |

### UI Components
| Package | Version | Purpose |
|---------|---------|---------|
| lucide-react | ^0.462.0 | Icons |
| shadcn/ui | Various | Component library |
| embla-carousel-react | ^8.3.0 | Carousel component |
| react-day-picker | ^8.10.1 | Date picker |
| sonner | ^1.5.0 | Toast notifications |
| vaul | ^0.9.3 | Accessible dialogs |

### Styling
| Package | Version | Purpose |
|---------|---------|---------|
| tailwind-merge | ^2.5.2 | Merge Tailwind classes |
| tailwindcss-animate | ^1.0.7 | Animation utilities |
| class-variance-authority | ^0.7.1 | Type-safe class variants |
| clsx | ^2.1.1 | Class name concatenation |

### Form Handling
| Package | Version | Purpose |
|---------|---------|---------|
| react-hook-form | ^7.53.0 | Form management |
| @hookform/resolvers | ^3.9.0 | Schema validation resolvers |
| input-otp | ^1.2.4 | OTP input component |

## Development Dependencies

### Build Tooling
| Package | Version | Purpose |
|---------|---------|---------|
| vite | ^5.4.1 | Build tool |
| @vitejs/plugin-react-swc | ^3.5.0 | React plugin for Vite |
| typescript | ^5.5.3 | Type checking |

### Styling
| Package | Version | Purpose |
|---------|---------|---------|
| tailwindcss | ^3.4.11 | CSS framework |
| autoprefixer | ^10.4.20 | CSS vendor prefixing |
| postcss | ^8.4.47 | CSS processing |
| @tailwindcss/typography | ^0.5.15 | Typography plugin |

### Linting & Formatting
| Package | Version | Purpose |
|---------|---------|---------|
| eslint | ^9.28.0 | JavaScript linting |
| prettier | ^3.5.3 | Code formatting |
| typescript-eslint | ^8.0.1 | TypeScript ESLint integration |

### Testing
| Package | Version | Purpose |
|---------|---------|---------|
| vitest | ^3.2.1 | Test runner |
| @testing-library/react | ^16.3.0 | React testing utilities |
| jsdom | ^26.1.0 | DOM implementation for testing |

## Maintenance Guidelines
1. Regularly check for updates using `npm outdated`
2. Update dependencies with `npm update`
3. Audit security vulnerabilities with `npm audit`
4. Review changelogs before major dependency updates

## Security Best Practices
1. **Dependency Auditing**: Run `npm audit` weekly
2. **Pinning Versions**: Use exact versions in production
3. **CI Checks**: Implement automated security scanning
4. **Vulnerability Monitoring**: Enable GitHub Dependabot