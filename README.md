# 🏒 The Otter Group - Premium Ice Hockey Experience Platform

> A comprehensive digital platform showcasing The Otter Group's premium ice hockey retail and dining experiences, designed to connect sports enthusiasts with cutting-edge gear, community events, and exceptional hospitality.

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?style=flat&logo=react&logoColor=61DAFB)](https://react.dev/)
[![Vite](https://img.shields.io/badge/Vite-646CFF?style=flat&logo=vite&logoColor=white)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=flat&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
[![npm](https://img.shields.io/badge/npm-CB3837?style=flat&logo=npm&logoColor=white)](https://www.npmjs.com/)

## 📋 Table of Contents

- [🎯 Project Overview](#project-overview)
- [✨ Features](#features)
- [🏗️ Architecture](#️-architecture)
- [🛠️ Technology Stack](#️-technology-stack)
- [📚 Project Documentation](#-project-documentation)
- [🚀 Getting Started](#-getting-started)
- [💻 Development Workflow](#-development-workflow)
- [📁 Project Structure](#-project-structure)
- [🎨 UI Components](#-ui-components)
- [🔧 Configuration](#-configuration)
- [📱 Responsive Design](#-responsive-design)
- [🎭 Theming](#-theming)
- [🧪 Testing](#-testing)
- [🚀 Deployment](#deployment)
- [🌐 Custom Domain](#-custom-domain)
- [📊 Performance](#-performance)
- [🔒 Security](#-security)
- [♿ Accessibility](#-accessibility)
- [🤝 Contributing](#-contributing)
- [📝 License](#-license)

## 🎯 Project Overview

**The Otter Group** is a modern, responsive web application that serves as the digital flagship for The Otter Group's ice hockey retail and dining empire. The platform seamlessly integrates premium sports retail experiences with community-driven dining concepts, creating a comprehensive ecosystem for ice hockey enthusiasts.

### 🎪 Live Demo

**Production URL**: [https://ottersportshub.com](https://ottersportshub.com)

### 🎨 Design Philosophy

The application embodies a modern, clean aesthetic that reflects the precision and elegance of ice hockey while maintaining accessibility and user-friendliness across all demographics. Every component is crafted with attention to detail, ensuring a premium user experience that matches the quality of The Otter Group's physical locations.

## ✨ Features

### 🏪 Core Business Features

- **🛍️ Premium Retail Showcase**: Interactive displays of cutting-edge ice hockey equipment and apparel
- **📧 Netlify Contact Form**: Comprehensive contact form with spam protection and accessibility features
- **🍽️ Dining Experience Hub**: Comprehensive restaurant and hospitality service presentations
- **👥 Community Integration**: Event management and community engagement platforms
- **📍 Location Services**: Multi-location support with detailed contact information
- **🎯 Target Market Analysis**: Sophisticated demographic and market segmentation displays

### 🎨 User Experience Features

- **📱 Fully Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **🌙 Dark/Light Theme Support**: Automatic theme detection with manual override capabilities
- **⚡ Lightning-Fast Performance**: Sub-second load times with optimized asset delivery
- **♿ WCAG 2.1 AA Compliance**: Full accessibility support for all users
- **🎭 Smooth Animations**: Carefully crafted micro-interactions and transitions
- **🔍 SEO Optimized**: Comprehensive meta tags and structured data implementation

### 🛠️ Technical Features

- **🔥 Hot Module Replacement**: Instant development feedback with Vite
- **📦 Component Library**: Comprehensive shadcn/ui component ecosystem
- **🎨 Utility-First CSS**: Tailwind CSS for rapid styling and customization
- **🔧 TypeScript Integration**: Full type safety and enhanced developer experience
- **📊 Performance Monitoring**: Built-in analytics and performance tracking
- **🔒 Security Headers**: Comprehensive security implementation

## 🏗️ Architecture

### 🏛️ Application Architecture

```ascii
┌─────────────────────────────────────────────────────────────┐
│                    The Otter Group                         │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer (React Components)                     │
│  ├── Hero Section (Video Background + CTA)                 │
│  ├── Business Segments (Retail + Dining)                   │
│  ├── Target Market Analysis                                │
│  ├── Community Features                                    │
│  └── Contact Information                                   │
├─────────────────────────────────────────────────────────────┤
│  State Management (React Query + Context)                  │
│  ├── Theme Management                                      │
│  ├── User Preferences                                      │
│  └── Application State                                     │
├─────────────────────────────────────────────────────────────┤
│  UI Framework (shadcn/ui + Tailwind CSS)                   │
│  ├── Design System Components                              │
│  ├── Responsive Grid System                                │
│  └── Animation Framework                                   │
├─────────────────────────────────────────────────────────────┤
│  Build System (Vite + TypeScript)                          │
│  ├── Hot Module Replacement                                │
│  ├── Asset Optimization                                    │
│  └── Bundle Splitting                                      │
└─────────────────────────────────────────────────────────────┘
```

### 🔄 Data Flow

1. **User Interaction** → Component Event Handlers
2. **State Updates** → React Query Cache Management
3. **UI Updates** → React Re-rendering with Optimizations
4. **Theme Changes** → CSS Variable Updates via Tailwind
5. **Route Navigation** → React Router with Lazy Loading

## 🛠️ Technology Stack

### 🎯 Core Technologies

| Technology       | Version | Purpose      | Documentation                              |
| ---------------- | ------- | ------------ | ------------------------------------------ |
| **React**        | ^18.3.1 | UI Framework | [React Docs](https://react.dev/)           |
| **TypeScript**   | ^5.5.3  | Type Safety  | [TS Docs](https://www.typescriptlang.org/) |
| **Vite**         | ^5.4.19 | Build Tool   | [Vite Docs](https://vitejs.dev/)           |
| **Tailwind CSS** | ^3.4.11 | Styling      | [Tailwind Docs](https://tailwindcss.com/)  |
| **React Router** | ^6.26.2 | Routing      | [Router Docs](https://reactrouter.com/)    |
| **npm**          | Latest  | Package Mgr  | [npm Docs](https://docs.npmjs.com/)        |

### 🎨 UI & Design

| Package                      | Version  | Purpose              |
| ---------------------------- | -------- | -------------------- |
| **shadcn/ui**                | Latest   | Component Library    |
| **Radix UI**                 | ^1.x     | Primitive Components |
| **Lucide React**             | ^0.462.0 | Icon System          |
| **class-variance-authority** | ^0.7.1   | Component Variants   |
| **clsx**                     | ^2.1.1   | Conditional Classes  |

### 🔧 Development Tools

| Tool             | Version  | Purpose             |
| ---------------- | -------- | ------------------- |
| **ESLint**       | ^9.28.0  | Code Linting        |
| **PostCSS**      | ^8.4.47  | CSS Processing      |
| **Autoprefixer** | ^10.4.20 | CSS Vendor Prefixes |

### 📦 Additional Libraries

- **@tanstack/react-query**: Server state management
- **react-hook-form**: Form handling and validation
- **date-fns**: Date manipulation utilities
- **embla-carousel-react**: Carousel components
- **next-themes**: Theme management

## 📚 Project Documentation

### 📝 Recent Updates

For a comprehensive list of recent project updates and issue resolutions, please refer to the [Project Updates](./docs/project_updates.md) document. This includes:

- Removal of legacy dependencies
- Performance optimizations
- Google Maps integration enhancements
- React context issue resolutions
- AWS Amplify build fixes

For detailed documentation of all dependencies, scripts, and maintenance guidelines, see:
[Package Documentation](./PACKAGE_DOCS.md)

## 🚀 Getting Started

### 📋 Prerequisites

Ensure you have the following installed on your development machine:

- **Bun**: Version 1.0.0 or higher
- **Node.js**: Version 18.0.0 or higher (optional)
- **Git**: For version control

#### 🔧 Installing Bun

```bash
# Install Bun
npm install -g npm@latest

# Verify installation
npm --version
```

### 🏁 Quick Start

#### 1. 📥 Clone the Repository

```bash
# Clone the repository
git clone https://github.com/your-organization/your-repository-name.git # (Replace with your actual repository URL)

# Navigate to project directory
cd otter-sports-hub
```

#### 2. 📦 Install Dependencies

```bash
# Install all project dependencies
npm install
```

#### 3. 🚀 Start Development Server

```bash
# Start the development server
npm run dev

# Server will start on http://localhost:8080
```

#### 4. 🌐 Open in Browser

Navigate to [http://localhost:8080](http://localhost:8080) to view the application.

### 🔧 Environment Setup

#### Development Environment Variables

Create a `.env.local` file in the root directory:

```env
# Development Configuration
VITE_APP_TITLE="The Otter Group - Development"
VITE_API_BASE_URL="http://localhost:3000/api"
VITE_ENABLE_ANALYTICS=false
VITE_DEBUG_MODE=true

# Theme Configuration
VITE_DEFAULT_THEME="light"
VITE_ENABLE_THEME_TOGGLE=true

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
```

## 💻 Development Workflow

### 🛠️ Available Scripts

| Command              | Description                     | Usage          |
| -------------------- | ------------------------------- | -------------- |
| `npm run dev`        | Start development server        | Development    |
| `npm run build`      | Build for production            | Deployment     |
| `npm run build:dev`  | Build with development settings | Testing        |
| `npm run preview`    | Preview production build        | Pre-deployment |
| `npm run lint`       | Run ESLint checks               | Code Quality   |
| `npm run test`       | Run unit tests with Vitest      | Testing        |

### 🔄 Development Process

#### 1. 🌿 Feature Development

```bash
# Create feature branch
git checkout -b feature/new-component

# Start development server
bun run dev

# Make changes and test
# ...

# Run linting
bun run lint

# Commit changes
git add .
git commit -m "feat: add new component with enhanced functionality"

# Push to remote
git push origin feature/new-component
```

#### 2. 🧪 Testing Changes

```bash
# Build for testing
bun run build:dev

# Preview build
bun run preview

# Test in different browsers and devices
```

#### 3. 🚀 Production Deployment

```bash
# Build for production
bun run build

# Deploy to your preferred hosting service
```

### 📝 Code Style Guidelines

#### TypeScript Conventions

```typescript
// ✅ Good: Descriptive interface names
interface BusinessSegmentProps {
  title: string;
  description: string;
  features: string[];
  isActive?: boolean;
}

// ✅ Good: Explicit return types for functions
const formatBusinessData = (data: RawBusinessData): BusinessSegmentProps => {
  return {
    title: data.name,
    description: data.desc,
    features: data.features || [],
    isActive: data.status === 'active'
  };
};

// ✅ Good: Proper component typing
const BusinessSegment: React.FC<BusinessSegmentProps> = ({
  title,
  description,
  features,
  isActive = false
}) => {
  return (
    <Card className={cn("transition-all duration-300", {
      "ring-2 ring-primary": isActive
    })}>
      {/* Component content */}
    </Card>
  );
};
```

#### CSS/Tailwind Conventions

```tsx
// ✅ Good: Organized class names
const HeroSection = () => (
  <section
    className="
    relative min-h-screen 
    flex items-center justify-center
    bg-gradient-to-br from-blue-900 to-blue-700
    text-white overflow-hidden
  "
  >
    <div
      className="
      container mx-auto px-6 
      text-center z-10
      animate-fade-in-up
    "
    >
      {/* Content */}
    </div>
  </section>
);

// ✅ Good: Using CSS variables for theming
const ThemedButton = () => (
  <button
    className="
    px-6 py-3 rounded-lg
    bg-primary text-primary-foreground
    hover:bg-primary/90 transition-colors
    focus:ring-2 focus:ring-primary focus:ring-offset-2
  "
  >
    Click me
  </button>
);
```

## 📁 Project Structure

```bash
otter-sports-hub/
├── .env.example
├── .gitignore
├── DEPLOYMENT_GUIDE.md
├── ENVIRONMENT_SETUP.md
├── PACKAGE_DOCS.md
├── PRD.md
├── README.md
├── amplify.yml
├── bun.lockb
├── components.json
├── eslint.config.js
├── index.html
├── package-lock.json
├── package.json
├── postcss.config.js
├── tailwind.config.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
├── public/
│   ├── favicon.ico
│   ├── hockey-bg.svg
│   ├── hockey-elements.svg
│   ├── placeholder.svg
│   └── robots.txt
├── supabase/
│   └── config.toml
└── src/
    ├── App.css
    ├── App.tsx
    ├── index.css
    ├── hooks/
    │   ├── use-form-field.ts
    │   ├── use-mobile.tsx
    │   ├── use-sidebar.ts
    │   └── use-toast.ts
    ├── integrations/
    │   └── supabase/
    ├── lib/
    │   ├── toast.ts
    │   └── utils.ts
    ├── main.tsx
    ├── pages/
    │   ├── Index.tsx
    │   └── NotFound.tsx
    └── vite-env.d.ts
    ├── components/
    │   ├── BusinessSegments.tsx
    │   ├── CommunitySection.tsx
    │   ├── ContactSection.tsx
    │   ├── GoogleMap.tsx
    │   ├── Hero.tsx
    │   ├── InteractiveFeaturesSection.tsx
    │   ├── LocationsSection.tsx
    │   ├── TargetMarket.tsx
    │   ├── TestimonialsSection.tsx
    │   └── ui/
    │       ├── accordion.tsx
    │       ├── alert-dialog.tsx
    │       ├── alert.tsx
    │       ├── aspect-ratio.tsx
    │       ├── avatar.tsx
    │       ├── badge-variants.ts
    │       ├── badge.tsx
    │       ├── breadcrumb.tsx
    │       ├── button-variants.ts
    │       ├── button.tsx
    │       ├── calendar.tsx
    │       ├── card.tsx
    │       ├── carousel.tsx
    │       ├── chart.tsx
    │       ├── checkbox.tsx
    │       ├── collapsible.tsx
    │       ├── command.tsx
    │       ├── context-menu.tsx
    │       ├── dialog.tsx
    │       ├── drawer.tsx
    │       ├── dropdown-menu.tsx
    │       ├── form.tsx
    │       ├── hover-card.tsx
    │       ├── input-otp.tsx
    │       ├── input.tsx
    │       ├── label.tsx
    │       ├── menubar.tsx
    │       ├── navigation-menu-trigger-style.ts
    │       ├── navigation-menu.tsx
    │       ├── pagination.tsx
    │       ├── popover.tsx
    │       ├── progress.tsx
    │       ├── radio-group.tsx
    │       ├── resizable.tsx
    │       ├── scroll-area.tsx
    │       ├── select.tsx
    │       ├── separator.tsx
    │       ├── sheet.tsx
    │       ├── sidebar-menu-button-variants.ts
    │       ├── sidebar.tsx
    │       ├── skeleton.tsx
    │       ├── slider.tsx
    │       ├── sonner.tsx
    │       ├── switch.tsx
    │       ├── table.tsx
    │       ├── tabs.tsx
    │       ├── textarea.tsx
    │       ├── toast.tsx
    │       ├── toaster.tsx
    │       ├── toggle-group.tsx
    │       ├── toggle-variants.ts
    │       ├── toggle.tsx
    │       └── tooltip.tsx
    ├── hooks/
    │   ├── use-form-field.ts
    │   ├── use-mobile.tsx
    │   ├── use-sidebar.ts
    │   └── use-toast.ts
    ├── lib/
    │   ├── toast.ts
    │   └── utils.ts
    └── pages/
        ├── Index.tsx
        └── NotFound.tsx
```

### 📂 Component Organization

#### 🏗️ Component Categories

1. **📄 Page Components** (`src/pages/`)

   - Full page layouts and routing
   - Data fetching and state management
   - SEO and meta tag management

2. **🧩 Feature Components** (`src/components/`)

   - Business logic components
   - Section-specific functionality
   - Complex interactive elements

3. **🎨 UI Components** (`src/components/ui/`)

   - Reusable design system components
   - shadcn/ui based elements
   - Styled primitive components

4. **🔧 Utility Hooks** (`src/hooks/`)
   - Custom React hooks
   - Shared logic extraction
   - State management utilities

## 🎨 UI Components

### 🎭 Design System

The application uses a comprehensive design system built on **shadcn/ui** and **Radix UI** primitives, ensuring consistency, accessibility, and maintainability.

#### 🎨 Color Palette

```css
:root {
  /* Primary Colors */
  --primary: 222.2 84% 4.9%; /* Deep blue-black */
  --primary-foreground: 210 40% 98%; /* Light text */

  /* Secondary Colors */
  --secondary: 210 40% 96%; /* Light gray */
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text */

  /* Accent Colors */
  --accent: 210 40% 96%; /* Subtle accent */
  --accent-foreground: 222.2 84% 4.9%; /* Accent text */

  /* Background Colors */
  --background: 210 22% 12%; /* Dark Navy #1f2831 */
  --foreground: 0 0% 100%; /* White text */

  /* Card Colors */
  --card: 210 22% 12%; /* Dark Navy cards */
  --card-foreground: 0 0% 100%; /* White card text */

  /* Border and Input */
  --border: 214.3 31.8% 91.4%; /* Light borders */
  --input: 214.3 31.8% 91.4%; /* Input borders */
  --ring: 222.2 84% 4.9%; /* Focus rings */
}

[data-theme="dark"] {
  /* Dark theme overrides */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... additional dark theme variables */
}
```

#### 🔤 Typography Scale

```css
/* Heading Styles */
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

/* Body Text */
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
```

#### 🎯 Component Examples

```tsx
// Button Component
<Button variant="primary" size="lg">
  Shop Now
</Button>

// Card Component
<Card className="overflow-hidden">
  <CardHeader>
    <CardTitle>Premium Hockey Sticks</CardTitle>
    <CardDescription>Professional-grade equipment</CardDescription>
  </CardHeader>
  <CardContent>
    <img src="/hockey-stick.jpg" alt="Hockey Stick" />
  </CardContent>
  <CardFooter className="flex justify-between">
    <span>$249.99</span>
    <Button>Add to Cart</Button>
  </CardFooter>
</Card>

// Accordion Component
<Accordion type="single" collapsible>
  <AccordionItem value="item-1">
    <AccordionTrigger>Equipment Care Guide</AccordionTrigger>
    <AccordionContent>
      Detailed instructions for maintaining hockey gear
    </AccordionContent>
  </AccordionItem>
  <AccordionItem value="item-2">
    <AccordionTrigger>Size Chart</AccordionTrigger>
    <AccordionContent>
      Comprehensive sizing guide for all equipment
    </AccordionContent>
  </AccordionItem>
</Accordion>
```

## 🔧 Configuration

### ⚙️ Vite Configuration

```ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          react: ['react', 'react-dom'],
          vendor: ['lodash', 'date-fns'],
        },
      },
    },
  },
  server: {
    port: 8080,
    open: true,
  },
});
```

### 🎨 Tailwind Configuration

```ts
import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        // ... additional colors
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.5s ease-out',
      },
      keyframes: {
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwindcss-animate'),
  ],
};

export default config;
```

### 📝 TypeScript Configuration

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## 📱 Responsive Design

### 📐 Breakpoint System

```ts
// Mobile-first responsive design
const ExampleComponent = () => (
  <div className="
    flex flex-col
    md:flex-row
    lg:space-x-8
  ">
    <div className="w-full md:w-1/2">
      {/* Content for medium screens and above */}
    </div>
    <div className="w-full md:w-1/2">
      {/* Content for medium screens and above */}
    </div>
  </div>
);

// Conditional rendering based on screen size
const { isMobile } = useMobile();

return (
  {isMobile ? (
    <MobileNavigation />
  ) : (
    <DesktopNavigation />
  )}
);
```

### 📱 Mobile-First Approach

```tsx
// Mobile menu implementation
const MobileMenu = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="md:hidden">
      <Button 
        variant="ghost" 
        size="icon"
        onClick={() => setIsOpen(true)}
      >
        <MenuIcon />
      </Button>
      
      <Drawer open={isOpen} onClose={() => setIsOpen(false)}>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Navigation</DrawerTitle>
          </DrawerHeader>
          <DrawerBody>
            <nav className="flex flex-col space-y-4">
              <Link href="/">Home</Link>
              <Link href="/retail">Retail</Link>
              <Link href="/dining">Dining</Link>
              <Link href="/community">Community</Link>
              <Link href="/contact">Contact</Link>
            </nav>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </div>
  );
};
```

### 📊 Performance Considerations

```tsx
// Lazy loading for components
const LazyCommunitySection = lazy(() => import('@/components/CommunitySection'));

const Index = () => (
  <Suspense fallback={<Skeleton className="h-96 w-full" />}>
    <LazyCommunitySection />
  </Suspense>
);

// Image optimization
<img
  src="/team-photo.jpg"
  alt="Otter Sports Team"
  width={1200}
  height={800}
  loading="lazy"
  className="w-full h-auto"
/>
```

## 🎭 Theming

### 🎨 CSS Variables

The project uses CSS custom properties for consistent theming across components:

```css
:root {
  --background: 222.2 84% 4.9%;
  --foreground: 0 0% 100%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 0 0% 100%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 0 0% 100%;
  --primary: 0 0% 100%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 0 0% 100%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 0 0% 100%;
  --accent: 0 0% 100%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 100%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}
```

#### 🎨 Usage in Components

```tsx
// Using CSS variables in Tailwind classes
<div className="bg-background text-foreground">
  <h1 className="text-primary">Heading</h1>
  <p className="text-muted-foreground">Description</p>
</div>

// Custom component styling
<Button className="bg-primary text-primary-foreground hover:bg-primary/90">
  Click me
</Button>
```

## 🚀 Deployment

### 🚀 Deployment Options

```bash
# Build production version
bun run build

# Preview production build locally
bun run preview

# Deploy to your preferred hosting service
# Example: netlify deploy --prod
```

#### 🚀 Automatic Deployment

```yml
# GitHub Actions deployment example
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: bun install
      - run: bun run build
      - run: bun run test
      - uses: netlify/actions/cli@master
        with:
          args: deploy --prod
          secrets: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          environment: production
```

### 🔍 Deployment Checklist

1. **✅ Verify Build**: Run `bun run build` locally to ensure no errors
2. **🧪 Run Tests**: Execute all tests with `bun run test`
3. **📝 Update Version**: Bump version in `package.json`
4. **📋 Changelog**: Update CHANGELOG.md with latest changes
5. **🔐 Environment Variables**: Ensure production environment variables are set
6. **🔒 Security Scan**: Run security audit with `bun audit`
7. **🚀 Deploy**: Execute deployment command

## 🌐 Custom Domain

### 🌐 Domain Setup

#### 📋 Prerequisites

- Registered domain name
- Access to domain registrar's DNS settings
- Production deployment URL

#### ⚙️ Setup Process

1. **Add Custom Domain in Your Hosting Provider's Dashboard**
   - Navigate to project settings
   - Select "Custom Domains"
   - Add your domain (e.g., `otterhockey.com`)

2. **Configure DNS Records**
   - Create CNAME record pointing to your hosting provider's domain
   - Example: `www.otterhockey.com CNAME ottersportshub.netlify.app`

3. **Configure Redirects**
   - Set up apex domain redirect (e.g., `otterhockey.com` → `www.otterhockey.com`)
   - Implement HTTPS redirect

#### 🔧 Advanced Configuration

```txt
; DNS Configuration Example
@        IN  A      ***********       ; Apex domain (if supported)
www      IN  CNAME  ottersportshub.netlify.app.
```

### 📚 Additional Resources

- [Netlify Custom Domains Documentation](https://docs.netlify.com/domains-https/custom-domains/)
- [DNS Configuration Best Practices](https://www.cloudflare.com/learning/dns/dns-records/)

## 📊 Performance

### ⚡ Performance Metrics

#### 🎯 Target Metrics

| Metric                 | Target Value |
|------------------------|--------------|
| **First Contentful Paint** | <1.0s        |
| **Largest Contentful Paint** | <2.5s      |
| **Time to Interactive** | <3.5s        |
| **Cumulative Layout Shift** | <0.1       |
| **Total Blocking Time** | <200ms       |

#### 🔧 Performance Optimizations

```ts
// Dynamic imports for code splitting
const BusinessSegments = lazy(() => import('@/components/BusinessSegments'));

// Image optimization with lazy loading
<img
  src="/retail-space.jpg"
  alt="Retail Space"
  width={1920}
  height={1080}
  loading="lazy"
  className="w-full h-auto"
/>

// Font optimization
<link
  rel="preload"
  href="/fonts/Inter.var.woff2"
  as="font"
  type="font/woff2"
  crossOrigin="anonymous"
/>
```

### 📈 Performance Monitoring

```ts
// Performance metrics collection
import { reportWebVitals } from '@/lib/performance';

// In main.tsx
reportWebVitals(metrics => {
  console.log(metrics);
  // Send to analytics service
});

// Example performance tracking
const startTime = performance.now();

// Execute expensive operation
processData();

const endTime = performance.now();
console.log(`Process took ${endTime - startTime}ms`);
```

## 🧹 Project Maintenance

### 🔧 Automated Cleanup

The project includes automated maintenance tools to keep the codebase optimized and clean.

#### 🤖 Cleanup Automation Script

```bash
# Run automated cleanup (dry-run mode first)
./scripts/cleanup-automation.sh --dry-run

# Apply changes after review
./scripts/cleanup-automation.sh
```

#### 📋 Maintenance Checklist

Refer to [`MAINTENANCE_CHECKLIST.md`](./MAINTENANCE_CHECKLIST.md) for:
- Monthly dependency audits
- Quarterly performance reviews
- Annual architecture assessments
- Automated cleanup procedures

#### 📊 Optimization Reports

Detailed optimization analysis available in:
- [`PROJECT_OPTIMIZATION_AUDIT_REPORT.md`](./PROJECT_OPTIMIZATION_AUDIT_REPORT.md) - Comprehensive audit findings
- [`PERFORMANCE_OPTIMIZATION_GUIDE.md`](./PERFORMANCE_OPTIMIZATION_GUIDE.md) - Performance improvement strategies

### 🔍 Regular Maintenance Tasks

#### Monthly Tasks
```bash
# Security audit
npm audit

# Check for outdated packages
npm outdated

# Run cleanup automation
./scripts/cleanup-automation.sh

# Bundle size analysis
npm run build
```

#### Quarterly Tasks
```bash
# Comprehensive dependency review
npm ls --depth=0

# Performance benchmarking
npm run build -- --analyze

# Code quality assessment
npm run lint -- --report-unused-disable-directives
```

### 📈 Monitoring and Alerts

- **Bundle Size**: Monitor for chunks >500kB
- **Dependencies**: Track security vulnerabilities
- **Performance**: Core Web Vitals monitoring
- **Build Time**: Track build performance trends

## 🔒 Security

### 🔐 Security Measures

#### 🛡️ Security Headers

```ts
// Security headers middleware
export const securityHeaders = {
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline' *.googleapis.com;
    img-src 'self' data:;
    font-src 'self' *.gstatic.com;
    connect-src 'self' api.otterhockey.com;
    frame-ancestors 'none';
    form-action 'self';
    base-uri 'self';
  `.replace(/\s+/g, ' '),
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
};
```

#### 🔍 Security Best Practices

1. **🔐 HTTPS Enforcement**: All traffic redirected to HTTPS
2. **🔒 Secure Cookies**: `HttpOnly` and `Secure` flags set
3. **🛡️ CSRF Protection**: Anti-CSRF tokens for form submissions
4. **🔑 Authentication**: JWT-based authentication with refresh tokens
5. **📝 Input Validation**: Zod schema validation for all user inputs
6. **🔒 Rate Limiting**: API request rate limiting
7. **🔍 Security Audits**: Regular `bun audit` and dependency checks

## ♿ Accessibility

### ♿ Accessibility Features

#### 🔍 Accessibility Features

- **🎨 High Contrast Mode**: Dedicated high contrast theme
- **🔊 Screen Reader Support**: ARIA attributes and semantic HTML
- **⌨️ Keyboard Navigation**: Full keyboard navigability
- **📱 Touch Targets**: Minimum 48x48px touch targets
- **📝 Focus Management**: Visible focus indicators
- **🗣️ Alternative Text**: Descriptive alt text for all images
- **📐 Responsive Design**: Adapts to various screen sizes

#### 🎨 Color and Contrast

```css
/* Minimum contrast ratios */
.text-primary {
  color: var(--foreground);
  background-color: var(--background);
  /* Contrast ratio: 7:1 */
}

.button {
  background-color: var(--primary);
  color: var(--primary-foreground);
  /* Contrast ratio: 4.5:1 */
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
```

## 🤝 Contributing

### 🌟 How to Contribute

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/your-feature`)
3. **Commit your changes** (`git commit -am 'Add some feature'`)
4. **Push to the branch** (`git push origin feature/your-feature`)
5. **Open a pull request**

#### 🚀 Getting Started

```bash
# Clone your fork
git clone https://github.com/your-username/otter-sports-hub.git

# Install dependencies
bun install

# Create development branch
git checkout -b feature/your-feature

# Start development server
bun run dev
```

#### 📝 Contributor Guidelines

- **📐 Code Style**: Follow existing patterns and ESLint rules
- **🧪 Testing**: Write tests for new features and bug fixes
- **📝 Documentation**: Update relevant documentation
- **♿ Accessibility**: Ensure all new components are accessible
- **📱 Responsive**: Verify functionality on all screen sizes

#### 🐛 Bug Reports

Please file bug reports on our [GitHub Issues](https://github.com/your-org/otter-sports-hub/issues) page. Include:

1. Steps to reproduce
2. Expected behavior
3. Actual behavior
4. Browser/OS version
5. Screenshots if applicable

#### 💡 Feature Requests

We welcome feature requests! Please create an issue with:

1. Description of the feature
2. Use cases and benefits
3. Proposed implementation (optional)

### 👥 Community Guidelines

- Be respectful and inclusive
- Provide constructive feedback
- Help maintain a welcoming environment
- Follow the project's code of conduct

## 📝 License

### 📄 MIT License

Copyright © 2025 The Otter Group

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

### 🌟 Special Thanks

- **Radix UI** for accessible primitives
- **Tailwind Labs** for the utility-first CSS framework
- **React Community** for the amazing UI library

### 🏒 About The Otter Group

The Otter Group is a premier provider of ice hockey retail and dining experiences, dedicated to serving the hockey community with premium products and exceptional service. Our mission is to create a hub where hockey enthusiasts can find everything they need to enjoy the sport they love.
