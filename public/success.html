<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Sent Successfully - The Otter Group</title>
    <meta name="description" content="Thank you for contacting The Otter Group. Your message has been sent successfully and we'll get back to you soon.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- CSS Variables for Dark Theme -->
    <style>
        :root {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 84% 4.9%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }
        
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-background text-foreground min-h-screen">
    <div class="min-h-screen flex items-center justify-center px-4 py-8">
        <div class="max-w-2xl w-full text-center fade-in">
            <!-- Success Icon -->
            <div class="mb-8 flex justify-center">
                <div class="success-animation bg-green-500 rounded-full p-6 inline-block">
                    <svg 
                        class="w-16 h-16 text-white" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                    >
                        <path 
                            stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M5 13l4 4L19 7"
                        />
                    </svg>
                </div>
            </div>
            
            <!-- Success Message -->
            <h1 class="text-4xl md:text-5xl font-bold mb-6 text-green-400">
                Message Sent Successfully!
            </h1>
            
            <div class="bg-card border border-border rounded-lg p-8 mb-8 shadow-lg">
                <p class="text-xl mb-4 text-card-foreground">
                    Thank you for contacting The Otter Group!
                </p>
                
                <p class="text-muted-foreground mb-6 leading-relaxed">
                    We've received your message and our team will get back to you as soon as possible. 
                    You should expect to hear from us within 24-48 hours during business days.
                </p>
                
                <div class="bg-secondary rounded-lg p-4 mb-6">
                    <h2 class="font-semibold mb-2 text-secondary-foreground">What happens next?</h2>
                    <ul class="text-left text-sm text-muted-foreground space-y-2">
                        <li class="flex items-start">
                            <span class="text-green-400 mr-2">✓</span>
                            Your message has been received and logged in our system
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-400 mr-2">✓</span>
                            Our team will review your inquiry and assign it to the appropriate specialist
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-400 mr-2">✓</span>
                            You'll receive a personalized response via email
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-400 mr-2">✓</span>
                            For urgent matters, we may contact you by phone if you provided a number
                        </li>
                    </ul>
                </div>
                
                <p class="text-sm text-muted-foreground">
                    <strong>Need immediate assistance?</strong><br>
                    Call us directly at 
                    <a 
                        href="tel:******-OTTER-1" 
                        class="text-primary hover:underline focus:underline focus:outline-none"
                    >
                        (555) OTTER-1
                    </a>
                </p>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                    href="/" 
                    class="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Return to Homepage
                </a>
                
                <a 
                    href="/#services" 
                    class="inline-flex items-center justify-center px-6 py-3 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Learn About Our Services
                </a>
            </div>
            
            <!-- Footer -->
            <div class="mt-12 pt-8 border-t border-border">
                <p class="text-sm text-muted-foreground">
                    &copy; 2024 The Otter Group. All rights reserved.
                </p>
            </div>
        </div>
    </div>
    
    <!-- Auto-redirect script (optional) -->
    <script>
        // Optional: Auto-redirect to homepage after 30 seconds
        // setTimeout(() => {
        //     window.location.href = '/';
        // }, 30000);
        
        // Track successful form submission (if using analytics)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'form_submit', {
                'event_category': 'Contact',
                'event_label': 'Contact Form Success'
            });
        }
    </script>
</body>
</html>