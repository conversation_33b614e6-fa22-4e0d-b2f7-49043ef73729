<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Failed to Send - The Otter Group</title>
    <meta name="description" content="There was an issue sending your message to The Otter Group. Please try again or contact us directly.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- CSS Variables for Dark Theme -->
    <style>
        :root {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 84% 4.9%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }
        
        .error-animation {
            animation: errorShake 0.5s ease-in-out;
        }
        
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse-red {
            animation: pulseRed 2s ease-in-out infinite;
        }
        
        @keyframes pulseRed {
            0%, 100% { background-color: rgb(239 68 68); }
            50% { background-color: rgb(220 38 38); }
        }
    </style>
</head>
<body class="bg-background text-foreground min-h-screen">
    <div class="min-h-screen flex items-center justify-center px-4 py-8">
        <div class="max-w-2xl w-full text-center fade-in">
            <!-- Error Icon -->
            <div class="mb-8 flex justify-center">
                <div class="error-animation pulse-red rounded-full p-6 inline-block">
                    <svg 
                        class="w-16 h-16 text-white" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                    >
                        <path 
                            stroke-linecap="round" 
                            stroke-linejoin="round" 
                            stroke-width="2" 
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                    </svg>
                </div>
            </div>
            
            <!-- Error Message -->
            <h1 class="text-4xl md:text-5xl font-bold mb-6 text-red-400">
                Message Failed to Send
            </h1>
            
            <div class="bg-card border border-border rounded-lg p-8 mb-8 shadow-lg">
                <p class="text-xl mb-4 text-card-foreground">
                    We're sorry, but there was an issue sending your message.
                </p>
                
                <p class="text-muted-foreground mb-6 leading-relaxed">
                    This could be due to a temporary technical issue or network problem. 
                    Please don't worry - your information is safe and we'd still love to hear from you.
                </p>
                
                <div class="bg-destructive/10 border border-destructive/20 rounded-lg p-4 mb-6">
                    <h2 class="font-semibold mb-3 text-destructive-foreground flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        What you can do:
                    </h2>
                    <ul class="text-left text-sm text-muted-foreground space-y-2">
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-2">•</span>
                            Try submitting the form again in a few minutes
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-2">•</span>
                            Check your internet connection and refresh the page
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-2">•</span>
                            Contact us directly using the phone number or email below
                        </li>
                        <li class="flex items-start">
                            <span class="text-yellow-400 mr-2">•</span>
                            Visit us in person at our location
                        </li>
                    </ul>
                </div>
                
                <div class="bg-secondary rounded-lg p-4 mb-6">
                    <h3 class="font-semibold mb-3 text-secondary-foreground">
                        Alternative Contact Methods:
                    </h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <strong>Phone:</strong>
                            <a 
                                href="tel:******-OTTER-1" 
                                class="ml-2 text-primary hover:underline focus:underline focus:outline-none"
                            >
                                (555) OTTER-1
                            </a>
                        </div>
                        
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <strong>Email:</strong>
                            <a 
                                href="mailto:<EMAIL>" 
                                class="ml-2 text-primary hover:underline focus:underline focus:outline-none"
                            >
                                <EMAIL>
                            </a>
                        </div>
                        
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <strong>Visit Us:</strong>
                            <span class="ml-2 text-muted-foreground">123 Hockey Lane, Sports City, SC 12345</span>
                        </div>
                    </div>
                </div>
                
                <p class="text-sm text-muted-foreground">
                    <strong>Business Hours:</strong><br>
                    Monday - Friday: 9:00 AM - 8:00 PM<br>
                    Saturday: 10:00 AM - 6:00 PM<br>
                    Sunday: 12:00 PM - 5:00 PM
                </p>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                    href="/#contact" 
                    class="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Try Again
                </a>
                
                <a 
                    href="/" 
                    class="inline-flex items-center justify-center px-6 py-3 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Return to Homepage
                </a>
                
                <a 
                    href="tel:******-OTTER-1" 
                    class="inline-flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    Call Us Now
                </a>
            </div>
            
            <!-- Footer -->
            <div class="mt-12 pt-8 border-t border-border">
                <p class="text-sm text-muted-foreground">
                    &copy; 2024 The Otter Group. All rights reserved.
                </p>
                <p class="text-xs text-muted-foreground mt-2">
                    If this problem persists, please let us know when you contact us directly.
                </p>
            </div>
        </div>
    </div>
    
    <!-- Error tracking script -->
    <script>
        // Track form submission error (if using analytics)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'form_error', {
                'event_category': 'Contact',
                'event_label': 'Contact Form Error'
            });
        }
        
        // Log error details for debugging
        console.warn('Contact form submission failed. User redirected to error page.');
        
        // Optional: Show additional help after some time
        setTimeout(() => {
            const helpBanner = document.createElement('div');
            helpBanner.className = 'fixed bottom-4 right-4 bg-primary text-primary-foreground p-4 rounded-lg shadow-lg max-w-sm';
            helpBanner.innerHTML = `
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium">Need immediate help?</p>
                        <p class="text-xs mt-1">Call us at (555) OTTER-1</p>
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-xs underline mt-1">Dismiss</button>
                    </div>
                </div>
            `;
            document.body.appendChild(helpBanner);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (helpBanner.parentElement) {
                    helpBanner.remove();
                }
            }, 10000);
        }, 5000);
    </script>
</body>
</html>