<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Animated hockey puck -->
  <defs>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Hockey puck with subtle glow -->
  <circle cx="50" cy="50" r="8" fill="#1e293b" filter="url(#glow)" opacity="0.6">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="translate"
      values="0,0; 20,0; 0,0; -20,0; 0,0"
      dur="8s"
      repeatCount="indefinite"/>
  </circle>
  
  <!-- Motion trail -->
  <ellipse cx="30" cy="50" rx="15" ry="2" fill="#3b82f6" opacity="0.2">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="translate"
      values="0,0; 20,0; 0,0; -20,0; 0,0"
      dur="8s"
      repeatCount="indefinite"/>
    <animate
      attributeName="opacity"
      values="0.2; 0.4; 0.2; 0.4; 0.2"
      dur="8s"
      repeatCount="indefinite"/>
  </ellipse>
</svg>