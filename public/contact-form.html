<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form - The Otter Group</title>
    <meta name="robots" content="noindex, nofollow">
</head>
<body>
    <!-- 
    This is a static HTML form for Netlify form detection.
    This file ensures Netlify can detect and process the form during build time.
    The actual form users interact with is in the React component.
    -->
    <form 
        name="contact-form" 
        method="POST" 
        data-netlify="true" 
        data-netlify-honeypot="bot-field"
        action="/success"
        style="display: none;"
    >
        <!-- Hidden field for Netlify form identification -->
        <input type="hidden" name="form-name" value="contact-form" />
        
        <!-- Honeypot field for spam protection -->
        <input type="text" name="bot-field" style="display: none;" />
        
        <!-- Form fields matching the React component -->
        <input type="text" name="name" required />
        <input type="email" name="email" required />
        <input type="tel" name="phone" />
        <input type="text" name="subject" required />
        <select name="category" required>
            <option value="">Select a category</option>
            <option value="hockey-equipment">Hockey Equipment</option>
            <option value="dining-reservations">Dining Reservations</option>
            <option value="events-parties">Events & Parties</option>
            <option value="equipment-fitting">Equipment Fitting</option>
            <option value="general-inquiry">General Inquiry</option>
            <option value="feedback">Feedback</option>
            <option value="support">Technical Support</option>
        </select>
        <select name="urgency" required>
            <option value="">Select urgency</option>
            <option value="low">Low - General inquiry</option>
            <option value="medium">Medium - Need response within 2-3 days</option>
            <option value="high">High - Need response within 24 hours</option>
            <option value="urgent">Urgent - Need immediate attention</option>
        </select>
        <textarea name="message" required></textarea>
        
        <button type="submit">Send Message</button>
    </form>
    
    <p>This page is used for Netlify form detection. Please visit the main site to use the contact form.</p>
    <a href="/">Go to Main Site</a>
</body>
</html>