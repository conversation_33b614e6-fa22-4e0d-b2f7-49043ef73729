<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Ice rink background with subtle texture -->
  <defs>
    <!-- Ice texture pattern -->
    <pattern id="iceTexture" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="#f8fafc" opacity="0.3"/>
      <path d="M10,10 Q50,30 90,10 Q50,50 10,90 Q50,70 90,90" stroke="#e2e8f0" stroke-width="0.5" fill="none" opacity="0.2"/>
    </pattern>
    
    <!-- Hockey puck trail pattern -->
    <pattern id="puckTrail" x="0" y="0" width="200" height="50" patternUnits="userSpaceOnUse">
      <circle cx="20" cy="25" r="3" fill="#1e293b" opacity="0.1"/>
      <circle cx="60" cy="25" r="2.5" fill="#1e293b" opacity="0.08"/>
      <circle cx="100" cy="25" r="2" fill="#1e293b" opacity="0.06"/>
      <circle cx="140" cy="25" r="1.5" fill="#1e293b" opacity="0.04"/>
      <circle cx="180" cy="25" r="1" fill="#1e293b" opacity="0.02"/>
    </pattern>
    
    <!-- Rink lines pattern -->
    <pattern id="rinkLines" x="0" y="0" width="400" height="200" patternUnits="userSpaceOnUse">
      <!-- Center line -->
      <line x1="200" y1="0" x2="200" y2="200" stroke="#3b82f6" stroke-width="2" opacity="0.1"/>
      <!-- Face-off circles -->
      <circle cx="100" cy="100" r="30" stroke="#3b82f6" stroke-width="1" fill="none" opacity="0.08"/>
      <circle cx="300" cy="100" r="30" stroke="#3b82f6" stroke-width="1" fill="none" opacity="0.08"/>
      <!-- Goal creases -->
      <path d="M50,80 Q70,100 50,120" stroke="#ef4444" stroke-width="1" fill="none" opacity="0.06"/>
      <path d="M350,80 Q330,100 350,120" stroke="#ef4444" stroke-width="1" fill="none" opacity="0.06"/>
    </pattern>
    
    <!-- Gradient overlay -->
    <radialGradient id="iceGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:0.4"/>
    </radialGradient>
  </defs>
  
  <!-- Base ice surface -->
  <rect width="1920" height="1080" fill="url(#iceTexture)"/>
  
  <!-- Rink elements -->
  <rect width="1920" height="1080" fill="url(#rinkLines)"/>
  
  <!-- Puck trails for movement -->
  <g transform="rotate(15 960 540)">
    <rect width="1920" height="1080" fill="url(#puckTrail)" opacity="0.3"/>
  </g>
  <g transform="rotate(-10 960 540)">
    <rect width="1920" height="1080" fill="url(#puckTrail)" opacity="0.2"/>
  </g>
  
  <!-- Hockey sticks crossed in corners -->
  <g opacity="0.05">
    <!-- Top left corner -->
    <g transform="translate(100,100) rotate(45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    <g transform="translate(120,80) rotate(-45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    
    <!-- Top right corner -->
    <g transform="translate(1820,100) rotate(135)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    <g transform="translate(1800,80) rotate(-135)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    
    <!-- Bottom corners -->
    <g transform="translate(100,980) rotate(-45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    <g transform="translate(120,1000) rotate(45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    
    <g transform="translate(1820,980) rotate(45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
    <g transform="translate(1800,1000) rotate(-45)">
      <rect x="-5" y="-100" width="10" height="200" fill="#8b5cf6" rx="5"/>
      <rect x="-3" y="-80" width="6" height="20" fill="#1e293b" rx="3"/>
    </g>
  </g>
  
  <!-- Subtle gradient overlay -->
  <rect width="1920" height="1080" fill="url(#iceGradient)"/>
  
  <!-- Floating hockey pucks -->
  <g opacity="0.03">
    <circle cx="300" cy="200" r="8" fill="#1e293b"/>
    <circle cx="1500" cy="300" r="6" fill="#1e293b"/>
    <circle cx="800" cy="800" r="7" fill="#1e293b"/>
    <circle cx="1200" cy="150" r="5" fill="#1e293b"/>
    <circle cx="400" cy="900" r="6" fill="#1e293b"/>
    <circle cx="1600" cy="700" r="8" fill="#1e293b"/>
  </g>
</svg>