# AWS Amplify Deployment Guide

## Overview
This guide helps you deploy The Otter Group application to AWS Amplify and configure the required Convex integration.

## Prerequisites
1. AWS Account with Amplify access
2. GitHub repository connected to AWS Amplify
3. Convex account and deployed functions

## Deployment Steps

### 1. Initial Deployment
The application is configured to deploy with a placeholder Convex URL to avoid build failures. The initial deployment will succeed but Convex features will not work until properly configured.

### 2. Setup Convex Backend
After your initial deployment succeeds:

1. **Login to Convex** (first time setup):
   ```bash
   npx convex login
   ```
   - Visit the provided URL (e.g., `https://auth.convex.dev/activate?user_code=XBVS-SFDF`)
   - Enter the device code when prompted
   - Link your existing deployment to your account if prompted
   - This creates a local deployment and saves credentials to `~/.convex/config.json`

2. **Deploy your Convex functions**:
   ```bash
   npx convex deploy
   ```

3. **Get your Convex deployment URLs**:
   - **Current deployment URL**: `https://admired-dolphin-223.convex.cloud`
   - **HTTP Actions URL**: `https://admired-dolphin-223.convex.site` (available as `process.env.CONVEX_SITE_URL` in Convex functions)
   - From Convex dashboard: `https://dashboard.convex.dev/d/local-nicholas_skateworkshockey_com-otter_sports_hub`
   - From terminal output after `convex deploy`
   - URL format: `https://your-deployment-id.convex.cloud`

### 3. Configure AWS Amplify Environment Variables

1. **Update AWS Amplify Environment Variables**:
   - Go to AWS Amplify Console
   - Select your app
   - Go to "App settings" > "Environment variables"
   - Update `VITE_CONVEX_URL` with your actual Convex URL: `https://admired-dolphin-223.convex.cloud`
   - For production deployments requiring automated access, you can also add:
     - Variable name: `CONVEX_DEPLOY_KEY`
     - Value: `prod:admired-dolphin-223|eyJ2MiI6IjJmYzc0ZTViNTZmYTRiNTQ5Mzk4ZGFkYWU0MjZkMzE2In0=`
     - **Important**: This key enables reading and writing data without login. Keep it secure and never commit to git.
   - Save changes

2. **Redeploy the application**:
   - Trigger a new build in AWS Amplify
   - Or push a new commit to trigger automatic deployment

### 4. Verification
After redeployment with the correct Convex URL:
- Check browser console for any Convex connection errors
- Verify that NHL ticker and other Convex-dependent features work
- No "placeholder client" warnings should appear in console

## Troubleshooting

### Build Failures
- Ensure `VITE_CONVEX_URL` is set in Amplify environment variables
- Check that the URL format is correct: `https://your-deployment-id.convex.cloud`

### Runtime Issues
- Verify Convex functions are deployed and accessible
- Check browser network tab for failed Convex API calls
- Ensure Convex URL is correctly configured in Amplify

### Local Development
For local development, create a `.env.local` file:
```
VITE_CONVEX_URL=https://your-deployment-id.convex.cloud
```

## Files Modified for Deployment
- `amplify.yml`: Added placeholder environment variable
- `src/providers/ConvexProvider.tsx`: Added graceful handling of missing/placeholder URL
- This deployment guide

## Next Steps
1. Complete initial deployment
2. Configure actual Convex URL
3. Redeploy with correct configuration
4. Test all functionality