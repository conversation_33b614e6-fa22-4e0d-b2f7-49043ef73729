# Deployment Guide

This document provides detailed instructions for deploying The Otter Group application to various environments.

## AWS Amplify Deployment

### Prerequisites

1. **AWS Account**: You need an AWS account with appropriate permissions to use AWS Amplify.
2. **Environment Variables**: Ensure all required environment variables are set in the AWS Amplify console.

### Configuration

The project includes an `amplify.yml` file that configures the build process:

```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm install
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: build
    files:
      - "**/*"
  cache:
    paths:
      - node_modules/**/*
  env:
    variables:
      VITE_GOOGLE_MAPS_API_KEY: ${VITE_GOOGLE_MAPS_API_KEY}
```

### Deployment Steps

1. **Connect Repository**:
   - In the AWS Amplify console, click "Connect app"
   - Choose your Git provider and repository
   - Select the branch you want to deploy

2. **Configure Build Settings**:
   - Review the build settings to ensure they match your `amplify.yml` file
   - Add any required environment variables (e.g., `VITE_GOOGLE_MAPS_API_KEY`)

3. **Deploy**:
   - Click "Save and deploy"
   - AWS Amplify will clone your repository, build the application, and deploy it

### Troubleshooting

If you encounter build issues:

1. **Check Environment Variables**: Ensure all required environment variables are set correctly.
2. **Review Build Logs**: Check the build logs for any errors or warnings.
3. **Package Lock**: Make sure the package-lock.json file is up to date and committed to the repository.
4. **Node Version**: Check that the Node.js version used by AWS Amplify is compatible with your dependencies.

## Manual Deployment

### Building for Production

```bash
# Install dependencies
npm install

# Build for production
npm run build
```

This will create a `build` directory with the production-ready files.

### Deploying to a Static Hosting Service

You can deploy the contents of the `build` directory to any static hosting service:

1. **Netlify**:
   ```bash
   netlify deploy --prod --dir=build
   ```

2. **Vercel**:
   ```bash
   vercel --prod build
   ```

3. **GitHub Pages**:
   ```bash
   # Using gh-pages package
   npx gh-pages -d build
   ```

## Environment Variables

The following environment variables are required for deployment:

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_GOOGLE_MAPS_API_KEY` | Google Maps API key for the map component | Yes |

## Post-Deployment Verification

After deploying, verify the following:

1. **Homepage**: Check that the homepage loads correctly.
2. **Google Maps**: Verify that the Google Maps component loads and displays the correct location.
3. **Images**: Ensure all images are loading properly.
4. **Responsive Design**: Test the application on different device sizes.
5. **Performance**: Run a Lighthouse test to check performance metrics.