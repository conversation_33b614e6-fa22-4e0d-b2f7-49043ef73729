# React Context Issues and Resolution

## Issue Description

During deployment to AWS Amplify, the application encountered the following error:

```
vendor-DhQPZVu2.js:11 Uncaught TypeError: Cannot read properties of undefined (reading 'createContext')
```

This error indicates that React's context API was not properly loaded before components tried to use it.

## Root Causes

1. **Code Splitting Issues**:
   - The default code splitting configuration was separating React and its dependencies into different chunks
   - This caused React's context API to be unavailable when components tried to use it

2. **Build Tool Differences**:
   - The project was developed using Bun locally, but deployed using npm on AWS Amplify
   - This led to discrepancies in dependency resolution and bundling

3. **React Global Availability**:
   - Some third-party libraries assume React is available globally
   - Without proper configuration, this assumption fails in production builds

## Solution Implemented

### 1. Disabled Code Splitting for React

```javascript
// vite.config.ts
rollupOptions: {
  output: {
    // Disable code splitting to avoid context issues
    manualChunks: undefined,
    // ...
  },
},
```

By disabling code splitting, we ensure that all React code is bundled together, preventing loading order issues.

### 2. Configured React Plugin Properly

```javascript
// vite.config.ts
react({
  jsxRuntime: 'automatic',
  jsxImportSource: 'react',
}),
```

This configuration ensures that React is properly imported in all JSX files.

### 3. Made React Available Globally

```javascript
// main.tsx
import React from 'react';
// ...
// Make React available globally to prevent "React is not defined" errors
if (typeof window !== 'undefined') {
  window.React = React;
}
```

This ensures that React is available on the window object, preventing "React is not defined" errors.

### 4. Added TypeScript Declarations

```typescript
// global.d.ts
import React from 'react';

declare global {
  interface Window {
    React: typeof React;
  }
}
```

This adds proper TypeScript declarations for the global React object.

## Lessons Learned

1. **Be Cautious with Code Splitting**:
   - Code splitting is a powerful optimization technique, but it can cause issues with libraries that rely on global objects
   - When using React context, ensure that React and its dependencies are loaded together

2. **Test Production Builds Locally**:
   - Always test production builds locally before deploying
   - Use the same build tools in development and production when possible

3. **Consider Global Availability**:
   - Some libraries assume React is available globally
   - Make React available globally when using such libraries

4. **Understand Build Tool Differences**:
   - Different build tools (npm, Bun, yarn) may handle dependencies differently
   - Be aware of these differences when switching between development and production environments

## Future Recommendations

1. **Use React.StrictMode**:
   - React.StrictMode helps identify potential problems in the application
   - It's especially useful for catching context-related issues

2. **Consider Alternative State Management**:
   - For complex applications, consider using a state management library like Redux or Zustand
   - These libraries are often more resilient to code splitting issues

3. **Monitor Bundle Size**:
   - Disabling code splitting increases the initial bundle size
   - Consider re-enabling code splitting once the core issues are resolved, with careful testing