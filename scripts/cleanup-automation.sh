#!/bin/bash

# Project Optimization Automation Script
# Description: Automated cleanup and maintenance procedures for Otter Sports Hub
# Usage: ./scripts/cleanup-automation.sh [--dry-run]

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/cleanup-$(date +%Y%m%d-%H%M%S).log"
DRY_RUN=false

# Parse arguments
if [[ "$1" == "--dry-run" ]]; then
    DRY_RUN=true
    echo "Running in dry-run mode - no changes will be made"
fi

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to safely remove files
safe_remove() {
    local file="$1"
    if [[ -f "$file" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            log "[DRY-RUN] Would remove: $file"
        else
            rm "$file"
            log "Removed: $file"
        fi
    else
        log "File not found (already removed?): $file"
    fi
}

# Function to check package manager consistency
check_package_managers() {
    log "Checking package manager consistency..."
    
    local lock_files=()
    [[ -f "$PROJECT_ROOT/package-lock.json" ]] && lock_files+=("npm")
    [[ -f "$PROJECT_ROOT/pnpm-lock.yaml" ]] && lock_files+=("pnpm")
    [[ -f "$PROJECT_ROOT/bun.lockb" ]] && lock_files+=("bun")
    
    if [[ ${#lock_files[@]} -gt 1 ]]; then
        log "WARNING: Multiple package managers detected: ${lock_files[*]}"
        log "Consider standardizing to one package manager"
        return 1
    else
        log "Package manager consistency: OK"
        return 0
    fi
}

# Function to clean unused assets
clean_unused_assets() {
    log "Cleaning unused assets..."
    
    local unused_assets=(
        "$PROJECT_ROOT/public/otter-logo.png"
        "$PROJECT_ROOT/public/otter pizza.png"
        "$PROJECT_ROOT/public/4fab3a16-4fff-40c2-9d46-5bf9f45347ea.jpeg"
        "$PROJECT_ROOT/public/team usa celebration.jpg"
        "$PROJECT_ROOT/public/favicon_io.zip"
    )
    
    for asset in "${unused_assets[@]}"; do
        safe_remove "$asset"
    done
}

# Function to analyze bundle size
analyze_bundle() {
    log "Analyzing bundle size..."
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cd "$PROJECT_ROOT"
        npm run build 2>&1 | tee -a "$LOG_FILE"
        
        # Check for large chunks
        if grep -q "larger than 500 kB" "$LOG_FILE"; then
            log "WARNING: Large chunks detected - consider code splitting"
        fi
    else
        log "[DRY-RUN] Would analyze bundle size"
    fi
}

# Function to check for unused dependencies
check_unused_dependencies() {
    log "Checking for unused dependencies..."
    
    # This would require additional tooling like depcheck
    # For now, just log the action
    log "Manual review recommended for: @googlemaps/js-api-loader, react-hook-form, input-otp, next-themes"
}

# Function to generate cleanup report
generate_report() {
    log "Generating cleanup report..."
    
    local report_file="$PROJECT_ROOT/logs/cleanup-report-$(date +%Y%m%d).md"
    
    cat > "$report_file" << EOF
# Cleanup Report - $(date '+%Y-%m-%d')

## Actions Performed
- Asset cleanup: $(grep "Removed:" "$LOG_FILE" | wc -l) files removed
- Package manager check: $(check_package_managers && echo "PASS" || echo "FAIL")
- Bundle analysis: Completed

## Recommendations
$(grep "WARNING:" "$LOG_FILE" || echo "No warnings")

## Log File
Full details: $LOG_FILE
EOF

    log "Report generated: $report_file"
}

# Main execution
main() {
    log "Starting automated cleanup process..."
    log "Project root: $PROJECT_ROOT"
    log "Dry run mode: $DRY_RUN"
    
    # Execute cleanup steps
    clean_unused_assets
    check_package_managers
    check_unused_dependencies
    analyze_bundle
    generate_report
    
    log "Cleanup process completed"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "Run without --dry-run to apply changes"
    fi
}

# Execute main function
main "$@"