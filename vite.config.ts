
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Conditionally import the image optimizer
let ViteImageOptimizer: any;
try {
  ViteImageOptimizer = require("vite-plugin-image-optimizer").ViteImageOptimizer;
} catch (error) {
  console.warn("vite-plugin-image-optimizer not available, skipping image optimization");
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    // Configure React plugin with JSX runtime
    react({
      jsxRuntime: 'automatic',
      jsxImportSource: 'react'
    }),
    // Enhanced image optimizer configuration
    ViteImageOptimizer && ViteImageOptimizer({
      png: {
        quality: mode === 'production' ? 75 : 80,
      },
      jpeg: {
        quality: mode === 'production' ? 75 : 80,
        progressive: true,
      },
      jpg: {
        quality: mode === 'production' ? 75 : 80,
        progressive: true,
      },
      webp: {
        quality: mode === 'production' ? 80 : 85,
        lossless: false,
      },
      avif: {
        quality: mode === 'production' ? 70 : 75,
      },
      svg: {
        multipass: true,
        plugins: [
          {
            name: 'preset-default',
            params: {
              overrides: {
                removeViewBox: false,
                cleanupIDs: false,
              },
            },
          },
        ],
      },
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    host: "::",
    port: 8080,
  },
  build: {
    outDir: "dist",
    minify: 'terser',
    chunkSizeWarningLimit: 500,
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : [],
      },
      mangle: {
        safari10: true,
      },
    },
    rollupOptions: {
      output: {
        // Optimized manual chunks for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-toast'],
          'query-vendor': ['@tanstack/react-query'],
          'maps-vendor': ['@react-google-maps/api', '@vis.gl/react-google-maps'],
          // Utility chunks
          'utils': ['clsx', 'tailwind-merge', 'class-variance-authority'],
          'icons': ['lucide-react'],
        },
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `assets/[name]-[hash].js`;
        },
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
           if (!assetInfo.name) return `assets/[name]-[hash].[ext]`;
           const info = assetInfo.name.split('.');
           const ext = info[info.length - 1];
           if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
             return `assets/images/[name]-[hash].[ext]`;
           }
           if (/css/i.test(ext)) {
             return `assets/css/[name]-[hash].[ext]`;
           }
           return `assets/[name]-[hash].[ext]`;
         }
      },
    },
    cssCodeSplit: true,
    sourcemap: mode !== 'production',
    assetsInlineLimit: 2048, // Reduced for better caching
    reportCompressedSize: false, // Faster builds
    target: 'es2020', // Modern browsers for better optimization
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
}));
