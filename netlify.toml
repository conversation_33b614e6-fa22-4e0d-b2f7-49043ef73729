# Netlify Configuration File
# This file configures Netlify's build settings, form handling, and redirects

[build]
  # Build command for the React application
  command = "npm run build"
  
  # Directory containing the built application
  publish = "dist"

# Redirect Rules
[[redirects]]
  # SPA fallback - serve index.html for all routes
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/success.html"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
    X-Robots-Tag = "noindex, nofollow"

[[headers]]
  for = "/error.html"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
    X-Robots-Tag = "noindex, nofollow"

# Context-specific settings
[context.production]
  command = "npm run build"

[context.deploy-preview]
  command = "npm run build"

[context.branch-deploy]
  command = "npm run build"