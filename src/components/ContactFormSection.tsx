/**
 * @file ContactFormSection.tsx
 * @description Comprehensive Netlify-compatible contact form with validation and accessibility
 * 
 * DESIGN PHILOSOPHY:
 * This component provides a full-featured contact form optimized for Netlify deployment
 * with built-in form detection, spam protection, and comprehensive validation.
 * 
 * COMPONENT ARCHITECTURE:
 * - Netlify form integration with proper attributes
 * - React Hook Form with Zod validation
 * - Comprehensive accessibility features
 * - Responsive design with loading states
 * 
 * ACCESSIBILITY FEATURES:
 * - Semantic HTML with proper fieldsets and legends
 * - ARIA attributes for screen readers
 * - Keyboard navigation support
 * - Error announcements and descriptions
 * 
 * NETLIFY INTEGRATION:
 * - Form detection via netlify attribute
 * - Hidden form-name field for identification
 * - Honeypot spam protection
 * - Success/error page redirects
 * 
 * @component ContactFormSection
 * @returns {JSX.Element} Complete contact form with Netlify integration
 */

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import '../styles/contact-form.css';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

// Comprehensive form validation schema
const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  phone: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, '')),
      'Please enter a valid phone number'
    ),
  
  subject: z
    .string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject must be less than 200 characters'),
  
  category: z
    .string()
    .min(1, 'Please select a category'),
  
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters'),
  
  urgency: z
    .string()
    .min(1, 'Please select urgency level'),
  
  // Honeypot field for spam protection (should remain empty)
  botField: z.string().max(0, 'Bot field must be empty').optional(),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

/**
 * ContactFormSection Component
 * 
 * Provides a comprehensive contact form with Netlify integration, validation,
 * and accessibility features. Includes spam protection and proper error handling.
 */
const ContactFormSection = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      category: '',
      message: '',
      urgency: '',
      botField: '', // Honeypot field
    },
  });

  /**
   * Handle form submission with Netlify integration
   */
  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Check honeypot field for spam protection
      if (data.botField && data.botField.length > 0) {
        throw new Error('Spam detected');
      }

      // Prepare form data for Netlify
      const formData = new FormData();
      formData.append('form-name', 'contact-form');
      
      // Add all form fields
      Object.entries(data).forEach(([key, value]) => {
        if (key !== 'botField' && value) {
          formData.append(key, value.toString());
        }
      });

      // Submit to Netlify
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(formData as any).toString(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setSubmitStatus('success');
      form.reset();
      
      toast({
        title: 'Message sent successfully!',
        description: 'Thank you for contacting us. We\'ll get back to you soon.',
      });

      // Redirect to success page after a delay
      setTimeout(() => {
        window.location.href = '/success';
      }, 2000);

    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
      
      toast({
        title: 'Failed to send message',
        description: 'Please try again or contact us directly.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section
      className="py-12 md:py-16 lg:py-24 bg-background contact-form-container"
      aria-labelledby="contact-form-title"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle 
              id="contact-form-title"
              className="text-2xl md:text-3xl lg:text-4xl font-bold"
            >
              Get in Touch
            </CardTitle>
            <CardDescription className="text-lg text-muted-foreground">
              Have questions about our hockey equipment or dining services? 
              We'd love to hear from you!
            </CardDescription>
          </CardHeader>

          <CardContent>
            {/* Netlify form with proper attributes */}
            <Form {...form}>
              <form
                name="contact-form"
                method="POST"
                data-netlify="true"
                data-netlify-honeypot="bot-field"
                onSubmit={form.handleSubmit(onSubmit)}
                className={`contact-form space-y-6 ${isSubmitting ? 'form-submitting' : ''} ${submitStatus === 'success' ? 'form-success' : ''} ${submitStatus === 'error' ? 'form-error' : ''}`}
                noValidate
                aria-describedby="form-description"
              >
                {/* Hidden field for Netlify form detection */}
                <input type="hidden" name="form-name" value="contact-form" />
                
                {/* Form description for screen readers */}
                <div id="form-description" className="sr-only">
                  Contact form for The Otter Group. All fields marked with asterisk are required.
                </div>

                {/* Honeypot field for spam protection (hidden from users) */}
                <div className="hidden" aria-hidden="true">
                  <FormField
                    control={form.control}
                    name="botField"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Don't fill this out if you're human</FormLabel>
                        <FormControl>
                          <Input {...field} tabIndex={-1} autoComplete="off" />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Personal Information Fieldset */}
                <fieldset className="space-y-4 border border-border rounded-lg p-4">
                  <legend className="text-lg font-semibold px-2">
                    Personal Information
                  </legend>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Name Field */}
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="required">
                            Full Name *
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your full name"
                              required
                              aria-describedby="name-description"
                              autoComplete="name"
                            />
                          </FormControl>
                          <FormDescription id="name-description">
                            Your first and last name
                          </FormDescription>
                          <FormMessage role="alert" />
                        </FormItem>
                      )}
                    />

                    {/* Email Field */}
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="required">
                            Email Address *
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="email"
                              placeholder="<EMAIL>"
                              required
                              aria-describedby="email-description"
                              autoComplete="email"
                            />
                          </FormControl>
                          <FormDescription id="email-description">
                            We'll use this to respond to your message
                          </FormDescription>
                          <FormMessage role="alert" />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Phone Field */}
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="tel"
                            placeholder="+****************"
                            aria-describedby="phone-description"
                            autoComplete="tel"
                          />
                        </FormControl>
                        <FormDescription id="phone-description">
                          Include country code for international numbers
                        </FormDescription>
                        <FormMessage role="alert" />
                      </FormItem>
                    )}
                  />
                </fieldset>

                {/* Message Details Fieldset */}
                <fieldset className="space-y-4 border border-border rounded-lg p-4">
                  <legend className="text-lg font-semibold px-2">
                    Message Details
                  </legend>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Category Field */}
                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="required">
                            Category *
                          </FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            required
                          >
                            <FormControl>
                              <SelectTrigger aria-describedby="category-description">
                                <SelectValue placeholder="Select a category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="hockey-equipment">Hockey Equipment</SelectItem>
                              <SelectItem value="dining-reservations">Dining Reservations</SelectItem>
                              <SelectItem value="events-parties">Events & Parties</SelectItem>
                              <SelectItem value="equipment-fitting">Equipment Fitting</SelectItem>
                              <SelectItem value="general-inquiry">General Inquiry</SelectItem>
                              <SelectItem value="feedback">Feedback</SelectItem>
                              <SelectItem value="support">Technical Support</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription id="category-description">
                            Help us route your message to the right team
                          </FormDescription>
                          <FormMessage role="alert" />
                        </FormItem>
                      )}
                    />

                    {/* Urgency Field */}
                    <FormField
                      control={form.control}
                      name="urgency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="required">
                            Urgency Level *
                          </FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            required
                          >
                            <FormControl>
                              <SelectTrigger aria-describedby="urgency-description">
                                <SelectValue placeholder="Select urgency" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">Low - General inquiry</SelectItem>
                              <SelectItem value="medium">Medium - Need response within 2-3 days</SelectItem>
                              <SelectItem value="high">High - Need response within 24 hours</SelectItem>
                              <SelectItem value="urgent">Urgent - Need immediate attention</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription id="urgency-description">
                            This helps us prioritize your request
                          </FormDescription>
                          <FormMessage role="alert" />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Subject Field */}
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="required">
                          Subject *
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Brief description of your inquiry"
                            required
                            aria-describedby="subject-description"
                            maxLength={200}
                          />
                        </FormControl>
                        <FormDescription id="subject-description">
                          A brief summary of what you need help with
                        </FormDescription>
                        <FormMessage role="alert" />
                      </FormItem>
                    )}
                  />

                  {/* Message Field */}
                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="required">
                          Message *
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Please provide details about your inquiry..."
                            className="min-h-[120px] resize-y"
                            required
                            aria-describedby="message-description"
                            maxLength={2000}
                          />
                        </FormControl>
                        <FormDescription id="message-description">
                          Provide as much detail as possible to help us assist you better
                          ({field.value?.length || 0}/2000 characters)
                        </FormDescription>
                        <FormMessage role="alert" />
                      </FormItem>
                    )}
                  />
                </fieldset>

                {/* Submit Button */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full sm:w-auto min-w-[200px] bg-white text-black border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 disabled:bg-gray-100 disabled:text-gray-500 disabled:border-gray-200"
                    size="lg"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending Message...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Send Message
                      </>
                    )}
                  </Button>

                  {submitStatus === 'success' && (
                    <div className="flex items-center text-green-600" role="status" aria-live="polite">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Message sent successfully!
                    </div>
                  )}

                  {submitStatus === 'error' && (
                    <div className="flex items-center text-red-600" role="alert" aria-live="assertive">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Failed to send message. Please try again.
                    </div>
                  )}
                </div>

                {/* Form Footer */}
                <div className="text-center text-sm text-muted-foreground pt-4 border-t">
                  <p>
                    By submitting this form, you agree to our privacy policy.
                    We'll only use your information to respond to your inquiry.
                  </p>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default ContactFormSection;
