import React, { useState, useEffect } from 'react';
import { usePerformanceMonitor } from '../hooks/use-performance-monitor';
import { cacheMonitor } from '../utils/caching-strategies';

/**
 * Performance monitoring component that displays real-time metrics
 * and cache statistics for development and debugging purposes
 */
interface PerformanceMetrics {
  fcp: number | null;
  lcp: number | null;
  fid: number | null;
  cls: number | null;
  ttfb: number | null;
  bundleSize: number | null;
  loadTime: number | null;
}

interface CacheStats {
  hitRatio: {
    hits: number;
    misses: number;
    ratio: number;
  };
  storage: {
    used: number;
    available: number;
    percentage: number;
  };
  memory: {
    size: number;
    keys: string[];
  };
}

const PerformanceMonitor: React.FC = () => {
  const { metrics, logMetrics } = usePerformanceMonitor();
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    hitRatio: {
      hits: 0,
      misses: 0,
      ratio: 0
    },
    storage: {
      used: 0,
      available: 0,
      percentage: 0
    },
    memory: {
      size: 0,
      keys: []
    }
  });
  const [isVisible, setIsVisible] = useState(false);
  const [swStats, setSwStats] = useState<Record<string, { entries: number; urls: string[] }> | null>(null);

  /**
   * Update cache statistics periodically
   */
  useEffect(() => {
    const updateCacheStats = () => {
      const stats = cacheMonitor.getStats();
      setCacheStats(stats);
    };

    // Update immediately and then every 5 seconds
    updateCacheStats();
    const interval = setInterval(updateCacheStats, 5000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Get service worker cache statistics
   */
  useEffect(() => {
    const getServiceWorkerStats = async () => {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        try {
          const messageChannel = new MessageChannel();
          
          messageChannel.port1.onmessage = (event) => {
            if (event.data.type === 'CACHE_STATS') {
              setSwStats(event.data.payload);
            }
          };
          
          navigator.serviceWorker.controller.postMessage(
            { type: 'GET_CACHE_STATS' },
            [messageChannel.port2]
          );
        } catch (error) {
          console.error('Failed to get service worker stats:', error);
        }
      }
    };

    getServiceWorkerStats();
    const interval = setInterval(getServiceWorkerStats, 10000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Toggle visibility with keyboard shortcut
   */
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + P to toggle performance monitor
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  /**
   * Get performance score color based on value
   */
  const getScoreColor = (value: number | null, thresholds: { good: number; poor: number }) => {
    if (value === null) return 'text-gray-400';
    if (value <= thresholds.good) return 'text-green-500';
    if (value <= thresholds.poor) return 'text-yellow-500';
    return 'text-red-500';
  };

  /**
   * Format metric value for display
   */
  const formatMetric = (value: number | null, unit: string = 'ms') => {
    if (value === null) return 'N/A';
    return `${value.toFixed(2)}${unit}`;
  };

  /**
   * Calculate total cache entries from service worker stats
   */
  const getTotalCacheEntries = () => {
    if (!swStats) return 0;
    return Object.values(swStats).reduce((total: number, cache) => {
      return total + (cache.entries || 0);
    }, 0);
  };

  // Only render in development mode
  if (process.env.NODE_ENV === 'production' && !isVisible) {
    return null;
  }

  return (
    <>
      {/* Toggle button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors"
        title="Toggle Performance Monitor (Ctrl+Shift+P)"
      >
        📊
      </button>

      {/* Performance monitor panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 max-w-md w-80 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Performance Monitor
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* Core Web Vitals */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Core Web Vitals
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>FCP (First Contentful Paint):</span>
                <span className={getScoreColor(metrics.fcp, { good: 1800, poor: 3000 })}>
                  {formatMetric(metrics.fcp)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>LCP (Largest Contentful Paint):</span>
                <span className={getScoreColor(metrics.lcp, { good: 2500, poor: 4000 })}>
                  {formatMetric(metrics.lcp)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>FID (First Input Delay):</span>
                <span className={getScoreColor(metrics.fid, { good: 100, poor: 300 })}>
                  {formatMetric(metrics.fid)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>CLS (Cumulative Layout Shift):</span>
                <span className={getScoreColor(metrics.cls ? metrics.cls * 1000 : null, { good: 100, poor: 250 })}>
                  {metrics.cls !== null ? metrics.cls.toFixed(3) : 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {/* Additional Metrics */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Metrics
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>TTFB (Time to First Byte):</span>
                <span className={getScoreColor(metrics.ttfb, { good: 800, poor: 1800 })}>
                  {formatMetric(metrics.ttfb)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Bundle Size:</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {metrics.bundleSize ? `${(metrics.bundleSize / 1024).toFixed(2)} KB` : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Load Time:</span>
                <span className={getScoreColor(metrics.loadTime, { good: 3000, poor: 5000 })}>
                  {formatMetric(metrics.loadTime)}
                </span>
              </div>
            </div>
          </div>

          {/* Cache Statistics */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Cache Performance
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Hit Ratio:</span>
                <span className={cacheStats.hitRatio.ratio > 0.8 ? 'text-green-500' : cacheStats.hitRatio.ratio > 0.6 ? 'text-yellow-500' : 'text-red-500'}>
                  {(cacheStats.hitRatio.ratio * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Total Requests:</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {cacheStats.hitRatio.hits + cacheStats.hitRatio.misses}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Cache Hits:</span>
                <span className="text-green-500">
                  {cacheStats.hitRatio.hits}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Cache Misses:</span>
                <span className="text-red-500">
                  {cacheStats.hitRatio.misses}
                </span>
              </div>
            </div>
          </div>

          {/* Service Worker Cache */}
          {swStats && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Service Worker Cache
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Cached Items:</span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {getTotalCacheEntries()}
                  </span>
                </div>
                {Object.entries(swStats).map(([cacheName, cache]) => {
                  const cacheData = cache as { entries?: number };
                  return (
                    <div key={cacheName} className="flex justify-between">
                      <span className="truncate">{cacheName}:</span>
                      <span className="text-gray-600 dark:text-gray-400">
                        {cacheData.entries || 0} items
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={() => logMetrics()}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs py-1 px-2 rounded transition-colors"
            >
              Refresh Metrics
            </button>
            <button
              onClick={() => {
                if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                  const messageChannel = new MessageChannel();
                  messageChannel.port1.onmessage = () => {
                    window.location.reload();
                  };
                  navigator.serviceWorker.controller.postMessage(
                    { type: 'CLEAR_CACHE' },
                    [messageChannel.port2]
                  );
                }
              }}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs py-1 px-2 rounded transition-colors"
            >
              Clear Cache
            </button>
          </div>

          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Press Ctrl+Shift+P to toggle
          </div>
        </div>
      )}
    </>
  );
};

export default PerformanceMonitor;