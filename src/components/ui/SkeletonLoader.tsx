/**
 * @file SkeletonLoader.tsx
 * @description Skeleton loading components for better perceived performance
 * 
 * Features:
 * - Animated skeleton placeholders
 * - Multiple skeleton variants
 * - Responsive design
 * - Accessibility support
 */

import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  variant?: 'default' | 'circular' | 'rectangular' | 'text';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
}

/**
 * Base skeleton component with customizable appearance
 * @param props - Skeleton configuration
 * @returns Animated skeleton placeholder
 */
export function Skeleton({
  className,
  variant = 'default',
  width,
  height,
  animation = 'pulse',
  ...props
}: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  const baseClasses = 'bg-muted animate-pulse';
  
  const variantClasses = {
    default: 'rounded-md',
    circular: 'rounded-full',
    rectangular: 'rounded-none',
    text: 'rounded-sm h-4'
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-gradient-pulse',
    none: ''
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={style}
      role="status"
      aria-label="Loading content"
      {...props}
    />
  );
}

/**
 * Hero section skeleton loader
 * Mimics the layout of the hero section while content loads
 */
export function HeroSkeleton() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-background text-foreground overflow-hidden">
      {/* Background skeleton */}
      <div className="absolute inset-0 bg-gradient-to-br from-muted/20 to-muted/40 animate-pulse" />
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-20 max-w-7xl pt-16 md:pt-24">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Badge skeleton */}
          <div className="flex justify-center">
            <Skeleton className="h-10 w-64 rounded-full" />
          </div>
          
          {/* Title skeleton */}
          <div className="space-y-3">
            <Skeleton className="h-16 w-full max-w-4xl mx-auto" />
            <Skeleton className="h-12 w-3/4 mx-auto" />
          </div>
          
          {/* Subtitle skeleton */}
          <Skeleton className="h-8 w-full max-w-2xl mx-auto" />
          
          {/* Description skeleton */}
          <div className="space-y-2 max-w-4xl mx-auto">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-5/6 mx-auto" />
            <Skeleton className="h-6 w-4/5 mx-auto" />
          </div>
          
          {/* Interactive cards skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto pt-8">
            <Skeleton className="h-32 w-full rounded-2xl" />
            <Skeleton className="h-32 w-full rounded-2xl" />
          </div>
        </div>
      </div>
    </section>
  );
}

/**
 * Card skeleton for loading states
 */
export function CardSkeleton() {
  return (
    <div className="p-6 space-y-4">
      <Skeleton className="h-4 w-3/4" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
      </div>
    </div>
  );
}

/**
 * Text block skeleton for content areas
 */
export function TextSkeleton({ lines = 3 }: { lines?: number }) {
  return (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className="h-4"
          width={i === lines - 1 ? '75%' : '100%'}
        />
      ))}
    </div>
  );
}

/**
 * Image skeleton with aspect ratio preservation
 */
export function ImageSkeleton({ 
  aspectRatio = '16/9',
  className 
}: { 
  aspectRatio?: string;
  className?: string;
}) {
  return (
    <Skeleton 
      className={cn('w-full', className)}
      style={{ aspectRatio }}
    />
  );
}

/**
 * Navigation skeleton for loading states
 */
export function NavSkeleton() {
  return (
    <div className="flex items-center justify-between p-4">
      <Skeleton className="h-8 w-32" />
      <div className="flex space-x-4">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  );
}