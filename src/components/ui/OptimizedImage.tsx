/**
 * @file OptimizedImage.tsx
 * @description High-performance image component with lazy loading, WebP support, and responsive sizing
 * 
 * Performance Features:
 * - Lazy loading with Intersection Observer
 * - WebP format with fallback support
 * - Responsive image sizing
 * - Loading states and error handling
 * - Preload critical images
 */

import { useState, useRef, useEffect, memo } from 'react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean; // For above-the-fold images
  sizes?: string; // Responsive sizes
  placeholder?: string; // Base64 or low-quality placeholder
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Optimized image component with performance enhancements
 * @param props - Image configuration props
 * @returns Optimized image element with lazy loading and format optimization
 */
const OptimizedImage = memo(({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  sizes = '100vw',
  placeholder,
  onLoad,
  onError
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const imgRef = useRef<HTMLImageElement>(null);
  
  // Use intersection observer for lazy loading (unless priority)
  const isInView = useIntersectionObserver(imgRef, {
    threshold: 0.1,
    rootMargin: '50px'
  });

  /**
   * Generate WebP source with fallback
   * @param originalSrc - Original image source
   * @returns WebP source URL
   */
  const getWebPSrc = (originalSrc: string): string => {
    if (originalSrc.includes('.svg')) return originalSrc;
    const extension = originalSrc.split('.').pop();
    return originalSrc.replace(`.${extension}`, '.webp');
  };

  /**
   * Handle successful image load
   */
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  /**
   * Handle image load error
   */
  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Load image when in view or priority
  useEffect(() => {
    if ((isInView || priority) && src && !isLoaded && !hasError) {
      setImageSrc(src);
    }
  }, [isInView, priority, src, isLoaded, hasError]);

  // Preload critical images
  useEffect(() => {
    if (priority && src) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
      
      return () => {
        document.head.removeChild(link);
      };
    }
  }, [priority, src]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Loading placeholder */}
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center"
          style={{ width, height }}
        >
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      )}
      
      {/* Error state */}
      {hasError && (
        <div 
          className="absolute inset-0 bg-muted flex items-center justify-center"
          style={{ width, height }}
        >
          <div className="text-muted-foreground text-sm">Failed to load image</div>
        </div>
      )}
      
      {/* Optimized image with WebP support */}
      <picture>
        <source 
          srcSet={imageSrc ? getWebPSrc(imageSrc) : ''} 
          type="image/webp" 
        />
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          width={width}
          height={height}
          sizes={sizes}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          } ${className}`}
          onLoad={handleLoad}
          onError={handleError}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
        />
      </picture>
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

export { OptimizedImage };