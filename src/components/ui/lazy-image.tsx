import { useRef, useState, useEffect, ImgHTMLAttributes } from 'react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { cn } from '@/lib/utils';

interface LazyImageProps extends ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  placeholderSrc?: string;
  className?: string;
}

/**
 * LazyImage component that only loads images when they enter the viewport
 * Improves initial page load performance by deferring offscreen images
 */
export function LazyImage({
  src,
  alt,
  placeholderSrc = '/placeholder.svg',
  className,
  ...props
}: LazyImageProps) {
  const [loaded, setLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholderSrc);
  const imgRef = useRef<HTMLImageElement>(null);
  const isInView = useIntersectionObserver(imgRef, { rootMargin: '200px' });

  // Load the image when it comes into view
  useEffect(() => {
    if (!isInView) return;

    const img = new Image();
    img.src = src;
    img.onload = () => {
      setCurrentSrc(src);
      setLoaded(true);
    };
  }, [isInView, src]);

  return (
    <img
      ref={imgRef}
      src={currentSrc}
      alt={alt}
      className={cn(
        'transition-opacity duration-300',
        loaded ? 'opacity-100' : 'opacity-50',
        className
      )}
      {...props}
    />
  );
}