/**
 * @file SectionHeader.tsx
 * @description A reusable component for displaying a standardized section header,
 *              including a title and a descriptive paragraph.
 */

interface SectionHeaderProps {
  title: string;
  description: string;
  className?: string; // Optional className for additional styling
}

const SectionHeader = ({ title, description, className }: SectionHeaderProps) => {
  return (
    <div className={`text-center mb-12 md:mb-16 ${className}`}>
      <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4 md:mb-6">
        {title}
      </h2>
      <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto">
        {description}
      </p>
    </div>
  );
};

export default SectionHeader;
