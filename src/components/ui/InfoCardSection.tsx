
/**
 * @file InfoCardSection.tsx
 * @description A reusable component for displaying a section with a title, description, and a grid of cards.
 */

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SectionHeader } from "@/components/ui"; // Or specific path if preferred
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface CardData {
  icon: LucideIcon;
  title: string;
  description?: string;
  details?: string[];
}

interface InfoCardSectionProps {
  sectionTitle: string;
  sectionDescription: string;
  cardsData: CardData[];
  gridCols?: string; // e.g., "md:grid-cols-2 lg:grid-cols-3"
  noWrapper?: boolean; // Option to skip the section wrapper
  className?: string;
  backgroundImage?: string; // URL for the background image
}

const InfoCardSection = ({
  sectionTitle,
  sectionDescription,
  cardsData,
  gridCols = "md:grid-cols-2 lg:grid-cols-3",
  noWrapper = false,
  className,
  backgroundImage,
}: InfoCardSectionProps) => {
  // Hover state management for glowing effects
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const content = (
    <>
      <SectionHeader title={sectionTitle} description={sectionDescription} />

      <div className={`grid grid-cols-1 ${gridCols} gap-6 md:gap-8 h-full`}>
        {/* Filter out the 'Support Network' card as requested */}
        {cardsData.filter(card => card.title !== "Support Network").map((card, index) => {
          const IconComponent = card.icon;
          const isHovered = hoveredCard === index;
          return (
            <Card
              key={index}
              className={`bg-card/70 backdrop-blur-sm h-full flex flex-col card-glow-ring ${
                isHovered ? 'card-glow-hover card-transform-hover' : 'card-glow-base'
              }`}
              onMouseEnter={() => setHoveredCard(index)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <CardHeader className="text-center pb-4 bg-transparent">
                <div className="w-12 h-12 md:w-16 md:h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="h-6 w-6 md:h-8 md:w-8 text-primary" />
                </div>
                <CardTitle className="text-xl md:text-2xl mb-2">
                  {card.title}
                </CardTitle>
                {card.description && (
                  <p className="text-muted-foreground text-xs md:text-sm mb-4">
                    {card.description}
                  </p>
                )}
              </CardHeader>
              <CardContent className="flex-1 flex flex-col bg-transparent px-6 pb-6 pt-0">
                {card.details && card.details.length > 0 && (
                  <ul className="space-y-2 text-sm md:text-base flex-1">
                    {card.details.map((detail, detailIndex) => (
                      <li
                        key={detailIndex}
                        className="text-card-foreground flex items-start"
                      >
                        <span className="inline-block mr-2">•</span>
                        <span className="flex-1">{detail}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </>
  );

  if (noWrapper) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {content}
      </div>
    );
  }

  return (
    <section className={cn("py-12 md:py-16 lg:py-24 bg-background relative overflow-hidden", className)}>
      {backgroundImage && (
        <>
          <div 
            className="absolute inset-0 w-full h-full bg-no-repeat bg-cover bg-center opacity-60 z-0" 
            style={{ backgroundImage: `url('${backgroundImage}')` }} 
          />
          <div className="absolute inset-0 w-full h-full bg-black opacity-40 z-0 pointer-events-none" />
        </>
      )}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10">
        {content}
      </div>
    </section>
  );
};

export default InfoCardSection;
