import { Zap, ArrowR<PERSON>, Play } from "lucide-react";
import { useState, useEffect, useRef } from "react";

import { toast } from 'sonner';
import { OptimizedImage } from './ui/OptimizedImage';
import { preloadCriticalImages, getOptimalImageQuality } from '../utils/image-optimization';
import { usePerformanceMonitor } from '../hooks/use-performance-monitor';
import { useIntersectionObserver } from '../hooks/use-intersection-observer';

/**
 * @file Hero.tsx
 * @description Primary hero section component featuring dynamic background elements and marketing content
 * 
 * Key Features:
 * - Responsive video background with overlay
 * - Animated decorative elements with coordinated timing
 * - Accessibility-focused content structure
 * - Performance-optimized visual effects
 * 
 * Design System Integration:
 * - Uses Tailwind CSS with custom theme extensions
 * - Implements shared animation timing constants
 * - Follows component spacing guidelines
 */

/**
 * Auto-playing YouTube video component for immediate background video
 * Loads and plays automatically when the component mounts
 */
const AutoPlayYouTubeVideo = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const videoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load video immediately when component mounts
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100); // Minimal delay for smooth rendering
    return () => clearTimeout(timer);
  }, []);

  return (
    <div ref={videoRef} className="absolute inset-0 w-full h-full overflow-hidden">
      {!isLoaded && (
        <div className="absolute inset-0 bg-gradient-to-br from-deep-navy/80 to-slate-gray/60 flex items-center justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-full p-6 animate-pulse">
            <Play className="h-12 w-12 text-white ml-1" fill="currentColor" />
          </div>
          <div className="absolute bottom-4 left-4 text-white/80 text-xs">
            Loading background video...
          </div>
        </div>
      )}
      {isLoaded && (
        <iframe
          className="absolute top-1/2 left-1/2 w-[177.77777778vh] h-[56.25vw] min-h-full min-w-full transform -translate-x-1/2 -translate-y-1/2 opacity-30 pointer-events-none"
          src="https://www.youtube.com/embed/pPm_18o28LI?autoplay=1&mute=1&loop=1&playlist=pPm_18o28LI&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&playsinline=1&enablejsapi=0"
          title="Background Video"
          frameBorder="0"
          allow="autoplay; encrypted-media"
          allowFullScreen={false}
        />
      )}
      <div className="absolute inset-0 bg-background/60 backdrop-blur-sm"></div>
    </div>
  );
};

/**
 * Hero component serving as the primary above-the-fold section
 * @remarks
 * - Implements WCAG 2.1 accessibility standards for video content
 * - Uses CSS transforms for performant animations
 * - Coordinate animation timings with global theme settings
 * 
 * @returns {JSX.Element} Hero section with interactive elements and marketing content
 */
const Hero = () => {
  const { logMetrics } = usePerformanceMonitor();
  
  // State for managing hover effects on hero cards
  const [hoveredCard, setHoveredCard] = useState(null);
  
  // Preload critical images for better performance
  useEffect(() => {
    const criticalImages = [
      '/hockey-bg.svg',
      '/hockey-elements.svg'
    ];
    preloadCriticalImages(criticalImages, 'high');
    
    // Log performance metrics
    logMetrics();
  }, [logMetrics]);

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-background text-foreground overflow-hidden">
      {/* Lazy-loaded Background Video */}
      <AutoPlayYouTubeVideo />



      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-20 max-w-7xl pt-16 md:pt-24">
        <div className="max-w-6xl mx-auto">
          {/* Modern Badge */}
          <div className="inline-flex items-center px-4 md:px-6 py-2 md:py-3 bg-primary/10 backdrop-blur-sm border border-primary/20 rounded-full mb-6 md:mb-8 animate-fade-in">
            <Zap className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-primary" />
            <span className="text-xs md:text-sm lg:text-base font-semibold text-primary tracking-wide">
              Premium Ice Hockey Experience
            </span>
          </div>

          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-6 md:mb-8 leading-tight text-white animate-fade-in delay-100">
            The <span className="text-white">Otter</span> Group
          </h1>

          <div className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl mb-8 md:mb-10 leading-relaxed text-white animate-fade-in delay-200">
            <span className="block sm:inline">Elite Hockey Equipment and Championship Dining</span> 
          </div>




          {/* Enhanced Interactive Stats - responsive grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto animate-fade-in delay-500">

            <a 
              href="https://iceboxhockey.com" 
              target="_blank" 
              rel="noopener noreferrer" 
              className={`block group text-center p-6 md:p-8 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/20 overflow-hidden relative transition-all duration-500 hover:-translate-y-2 hover:border-primary cursor-pointer card-glow-ring card-transform-hover ${
                hoveredCard === 'retail' ? 'card-glow-hover' : 'card-glow-base'
              }`}
              onMouseEnter={() => setHoveredCard('retail')}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-primary/40 via-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out transform group-hover:scale-105"></div>
              <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-4 group-hover:text-accent transition-colors duration-300 leading-tight">
                Hockey Retail Stores and Pro Shop
              </div>
 
               <div className="mt-4 text-sm md:text-base text-primary/90 group-hover:text-accent transition-colors duration-300 flex items-center justify-center gap-1">
                Explore our stores <ArrowRight className="w-4 h-4" />
              </div>
              <div className="absolute bottom-0 left-0 w-full h-[2px] bg-primary/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></div>
            </a>
            <a 
              href="https://www.theangryotter.com/" 
              target="_blank" 
              rel="noopener noreferrer" 
              className={`block group text-center p-6 md:p-8 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/20 overflow-hidden relative transition-all duration-500 hover:-translate-y-2 hover:border-primary cursor-pointer card-glow-ring card-transform-hover ${
                hoveredCard === 'restaurant' ? 'card-glow-hover' : 'card-glow-base'
              }`}
              onMouseEnter={() => setHoveredCard('restaurant')}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-primary/40 via-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 ease-out transform group-hover:scale-105"></div>
              <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>
              <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-4 group-hover:text-accent transition-colors duration-300 leading-tight">
                Hockey Themed Restaurant and Bar
              </div>

              <div className="mt-4 text-sm md:text-base text-primary/90 group-hover:text-accent transition-colors duration-300 flex items-center justify-center gap-1">
                Explore our restaurants <ArrowRight className="w-4 h-4" />
              </div>
              <div className="absolute bottom-0 left-0 w-full h-[2px] bg-primary/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></div>
            </a>
          </div>


        </div>
      </div>
    </section>
  );
};



export default Hero;
