/**
 * COMMUNITY ENGAGEMENT SECTION COMPONENT
 * 
 * DESIGN PHILOSOPHY:
 * This component showcases the community-focused aspects of The Otter Group,
 * emphasizing social connection, local engagement, and community building.
 * The design balances informational content with emotional appeal to create
 * a sense of belonging and shared purpose.
 * 
 * ARCHITECTURE DECISIONS:
 * - Card-based layout for clear feature separation and visual hierarchy
 * - Icon-driven design for immediate visual recognition
 * - Responsive grid system adapting from single to multi-column layouts
 * - Consistent spacing and typography following design system principles
 * 
 * CONTENT STRATEGY:
 * - Four key engagement pillars: Community, Events, Achievements, Support
 * - Each pillar represents a different aspect of community involvement
 * - Content emphasizes benefits and emotional connection
 * - Clear calls-to-action implied through engaging descriptions
 * 
 * VISUAL DESIGN:
 * - Warm, inviting color palette to convey community spirit
 * - Hover effects create interactive engagement
 * - Consistent iconography reinforces brand identity
 * - Balanced white space for comfortable reading experience
 * 
 * ACCESSIBILITY FEATURES:
 * - Semantic HTML structure with proper heading hierarchy
 * - High contrast ratios for text readability
 * - Keyboard navigation support
 * - Screen reader friendly content structure
 * 
 * PERFORMANCE CONSIDERATIONS:
 * - Lightweight component with minimal dependencies
 * - CSS-based animations for smooth performance
 * - Optimized for fast rendering across devices
 */

import { InfoCardSection } from "@/components/ui"; // Updated import
import { Users, Calendar, Trophy, Heart } from "lucide-react";

/**
 * COMMUNITY ENGAGEMENT SECTION COMPONENT
 * 
 * Renders a comprehensive overview of community engagement opportunities
 * and benefits, designed to foster connection and participation.
 */
const CommunitySection = () => {
  /**
   * EVENT CATEGORIES CONFIGURATION
   * 
   * Defines the four pillars of community engagement at The Otter Group.
   * Each category represents a different aspect of community involvement
   * and is designed to appeal to various motivations for participation.
   * 
   * CATEGORY DESIGN PRINCIPLES:
   * - Icons chosen for immediate visual recognition and emotional connection
   * - Titles are concise yet descriptive of the engagement type
   * - Descriptions emphasize benefits and emotional outcomes
   * - Content balances practical information with aspirational messaging
   * 
   * CONTENT STRATEGY:
   * - Community Building: Focuses on social connection and relationship formation
   * - Regular Events: Emphasizes consistency and ongoing engagement opportunities
   * - Achievements: Highlights recognition and celebration of success
   * - Support Network: Emphasizes care, mentorship, and mutual support
   */
  const eventCategories = [
    {
      icon: Users, // Represents community and social connection
      title: "Community Building",
      description: "Join local sports enthusiasts and build lasting friendships through shared passion for athletics and competition."
    },
    {
      icon: Calendar, // Represents scheduled activities and consistency
      title: "Regular Events",
      description: "Participate in weekly tournaments, seasonal leagues, and special community gatherings throughout the year."
    },
    {
      icon: Trophy, // Represents achievement and recognition
      title: "Achievements",
      description: "Celebrate victories, recognize outstanding performances, and showcase community accomplishments together."
    },
    {
      icon: Heart, // Represents care, support, and emotional connection
      title: "Support Network",
      description: "Experience encouragement, mentorship, and camaraderie from fellow community members and local athletes."
    }
  ];

  return (
    /**
     * INFO CARD SECTION IMPLEMENTATION
     * 
     * Leverages the reusable InfoCardSection component for consistency
     * across the application. This approach ensures:
     * 
     * DESIGN CONSISTENCY:
     * - Uniform spacing and typography with other sections
     * - Consistent card styling and hover effects
     * - Standardized responsive behavior
     * 
     * MAINTAINABILITY:
     * - Changes to card styling affect all sections uniformly
     * - Centralized component logic reduces code duplication
     * - Easy to update design patterns across the application
     * 
     * CONTENT STRUCTURE:
     * - Title and description provide section context
     * - Cards array enables dynamic rendering of engagement categories
     * - Custom className allows for section-specific styling adjustments
     */
    <InfoCardSection
      sectionTitle="Community Engagement"
      sectionDescription="Connect with fellow sports enthusiasts and become part of our vibrant community ecosystem."
      cardsData={eventCategories}
      className="py-12 md:py-16 lg:py-24" // Responsive vertical padding
      backgroundImage="/SCR-20250615-npku.jpeg"
    />
  );
};

export default CommunitySection;
