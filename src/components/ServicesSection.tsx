/**
 * @file BusinessSegments.tsx
 * @description Enhanced business segments with interactive elements and animations
 * 
 * Key Features:
 * - Interactive hover effects and state management
 * - Floating background animations
 * - Enhanced visual design with gradients
 * - Responsive stats section
 */

import { InfoCardSection } from "@/components/ui";
import { useState } from "react";
import { Sparkles, TrendingUp, ShoppingBag, Utensils } from "lucide-react";

const BusinessSegments = () => {
  const [activeSegment, setActiveSegment] = useState<string | null>(null);

  return (
    <div className="relative py-12 md:py-16 bg-gradient-to-br from-background via-muted/10 to-background overflow-hidden">
      <div 
        className="absolute inset-0 w-full h-full bg-no-repeat bg-cover bg-center opacity-60 -z-10" 
        style={{
          backgroundImage: "url('/d16fc91b-7f8e-49b0-82d2-a1131d066e9e.jpeg')",
          backgroundAttachment: 'fixed',
          backgroundSize: 'cover',
          backgroundPosition: 'center center'
        }} 
      />
      <div className="absolute inset-0 w-full h-full bg-black opacity-40 -z-10 pointer-events-none" />
      <div className="relative">
      <div className="container mx-auto px-6 sm:px-8 lg:px-12 max-w-7xl">
        {/* Enhanced Header with Interactive Elements */}
        <div className="text-center mb-12 md:mb-16 relative">
          {/* Floating Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-10 left-1/4 w-20 h-20 bg-primary/10 rounded-full animate-float-particle" />
            <div className="absolute top-20 right-1/3 w-16 h-16 bg-secondary/10 rounded-full animate-float-particle" style={{ animationDelay: '1s' }} />
            <div className="absolute bottom-10 left-1/3 w-12 h-12 bg-accent/10 rounded-full animate-float-particle" style={{ animationDelay: '2s' }} />
          </div>
          
          <div className="relative z-10">
            <div className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-primary/10 rounded-full border border-primary/20">
              <Sparkles className="h-5 w-5 text-primary animate-pulse" />
              <span className="text-xs md:text-sm font-semibold text-primary">Dual Excellence</span>
              <TrendingUp className="h-5 w-5 text-primary" />
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-foreground mb-4">
              Our Business <span className="text-white">Segments</span>
            </h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
              Our mission is to serve the entire hockey community: families, sports enthusiasts, and players of all ages and skill levels, providing top-tier equipment and exceptional dining experiences.
            </p>

            {/* Segment Toggle Buttons - Removed as per request */}
            <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
              {/* Retail and Dining buttons removed */}
            </div>
          </div>
        </div>

        {/* Interactive Segment Cards - Side by Side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-10 lg:gap-12 items-center justify-center max-w-6xl mx-auto">
          {/* Retail Division Card */}
          <div 
            className={`transition-all duration-500 hover:scale-[1.02] h-full min-h-[500px] shadow-[0_0_25px_rgba(255,255,255,0.4)] border border-white/30 rounded-lg flex items-center justify-center ${
              activeSegment === 'retail' ? 'ring-2 ring-primary/30 shadow-[0_0_35px_rgba(255,255,255,0.6)]' : 'shadow-[0_0_25px_rgba(255,255,255,0.4)]'
            }`}
            onMouseEnter={() => setActiveSegment('retail')}
            onMouseLeave={() => setActiveSegment(null)}
          >
            <div className="w-full h-full flex flex-col justify-center p-8">
              <div className="text-center mb-4">
                <h3 className="text-2xl font-bold mb-2"></h3>
                <p className="text-muted-foreground"></p>
              </div>
              
              <div className="flex-1 flex items-center justify-center">
                <div className="bg-card/70 border border-border/70 shadow-lg backdrop-blur-sm p-10 w-full flex flex-col justify-center">
                  <div className="text-center mb-10">
                    <div className="w-20 h-20 md:w-24 md:h-24 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-8">
                      <ShoppingBag className="h-8 w-8 md:h-10 md:w-10 text-primary" />
                    </div>
                    <h4 className="text-2xl md:text-3xl lg:text-4xl font-semibold mb-4">Professional Equipment</h4>
                    <p className="text-muted-foreground text-base leading-relaxed">Expert fitting and consultation services for all your hockey needs</p>
                  </div>
                  
                  <ul className="space-y-4 text-base md:text-lg">
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Professional equipment fitting and consultation services</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Wide selection of sticks, skates, protective gear, and accessories</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Expert advice from former players and hockey enthusiasts</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Custom team orders and bulk purchasing options available</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Restaurant Division Card */}
          <div 
            className={`transition-all duration-500 hover:scale-[1.02] h-full min-h-[500px] shadow-[0_0_25px_rgba(255,255,255,0.4)] border border-white/30 rounded-lg flex items-center justify-center ${
              activeSegment === 'dining' ? 'ring-2 ring-primary/30 shadow-[0_0_35px_rgba(255,255,255,0.6)]' : 'shadow-[0_0_25px_rgba(255,255,255,0.4)]'
            }`}
            onMouseEnter={() => setActiveSegment('dining')}
            onMouseLeave={() => setActiveSegment(null)}
          >
            <div className="w-full h-full flex flex-col justify-center p-8">
              <div className="text-center mb-4">
                <h3 className="text-2xl font-bold mb-2"></h3>
                <p className="text-muted-foreground"></p>
              </div>
              
              <div className="flex-1 flex items-center justify-center">
                <div className="bg-card/70 border border-border/70 shadow-lg backdrop-blur-sm p-10 w-full flex flex-col justify-center">
                  <div className="text-center mb-10">
                    <div className="w-20 h-20 md:w-24 md:h-24 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-8">
                      <Utensils className="h-8 w-8 md:h-10 md:w-10 text-primary" />
                    </div>
                    <h4 className="text-2xl md:text-3xl lg:text-4xl font-semibold mb-4">Sports Dining Experience</h4>
                    <p className="text-muted-foreground text-base leading-relaxed">Hockey culture meets exceptional food and atmosphere perfectly</p>
                  </div>
                  
                  <ul className="space-y-4 text-base md:text-lg">
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Game viewing on multiple large screen TVs and displays</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Amazing pizza and fresh food perfect for any event or gathering</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Private event spaces for team celebrations and corporate functions</span>
                    </li>
                    <li className="text-card-foreground flex items-start">
                      <span className="inline-block mr-2">•</span>
                      <span className="flex-1">Live entertainment and special events during hockey season available</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button - Text removed as per request */}
        <div className="mt-12 text-center">
          {/* Intentionally left empty */}
        </div>
      </div>
      </div>
    </div>
  );
};

export default BusinessSegments;
