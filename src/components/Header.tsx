/**
 * @file Header.tsx
 * @description Empty header container with no content
 */

/**
 * Header component - Empty container with minimal styling
 * @returns {JSX.Element} Empty header container
 */
const Header = () => {
  return (
    <header className="header-sticky sticky top-0 w-full bg-background/95 border-b border-border shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="h-16">
          {/* Empty header container - all content removed */}
        </div>
      </div>
    </header>
  );
};

export default Header;