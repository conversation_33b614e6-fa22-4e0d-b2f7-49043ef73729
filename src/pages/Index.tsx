
/**
 * @file Index.tsx
 * This component serves as the main landing page for The Otter Group application.
 * It aggregates various sections of the website into a single, cohesive page.
 */

// Import React's lazy loading capabilities and performance monitoring
import { useEffect } from 'react';
import { createLazyComponent } from '@/utils/code-splitting';
import HeroSection from "@/components/HeroSection"; // The main hero section, usually at the top of the page.
import { usePerformanceMonitor } from '@/hooks/use-performance-monitor';

// Create optimized lazy components with skeleton loading states
const ServicesSection = createLazyComponent(
  () => import("@/components/ServicesSection"),
  { 
    skeleton: 'card',
    skeletonProps: { height: '400px', className: 'mx-4' },
    preload: true 
  }
); // Section detailing different business areas.





const CommunityEngagementSection = createLazyComponent(
  () => import("@/components/CommunityEngagementSection"),
  { 
    skeleton: 'text',
    skeletonProps: { lines: 4, className: 'max-w-6xl mx-auto' }
  }
); // Section for community engagement features.

const ContactFormSection = createLazyComponent(
  () => import("@/components/ContactFormSection"),
  { 
    skeleton: 'custom',
    skeletonProps: { height: '500px', className: 'max-w-2xl mx-auto' }
  }
); // Section with contact information.

/**
 * The Index component.
 * This functional component structures the main landing page of the application.
 * It renders a sequence of major sections:
 * - HeroSection: The main introductory hero section.
 * - ServicesSection: Showcases the different segments of the business.

 * - CommunityEngagementSection: Highlights community features and engagement.
 * - ContactFormSection: Provides contact information and methods.
 * The page is set against a white background and ensures a minimum screen height.
 */
const Index = () => {
  const { logMetrics } = usePerformanceMonitor();
  
  // Log page-specific performance metrics
  useEffect(() => {
    const timer = setTimeout(() => {
      logMetrics();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [logMetrics]);
  
  return (
    <div className="min-h-screen bg-background hockey-background">
      {/* Floating hockey elements for visual interest */}
      <div className="floating-hockey-element"></div>
      <div className="floating-hockey-element"></div>
      <div className="floating-hockey-element"></div>
      
      {/* Renders the main hero section. */}
      <HeroSection />
      
      {/* Optimized lazy loaded sections with error boundaries */}
      <ServicesSection />

      <CommunityEngagementSection />
      <ContactFormSection />
    </div>
  );
};

export default Index;
