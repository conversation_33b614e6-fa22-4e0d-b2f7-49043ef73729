// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://srvyvfswcxemykxfxnaj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNydnl2ZnN3Y3hlbXlreGZ4bmFqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MjI5NjksImV4cCI6MjA2NTQ5ODk2OX0.QXnIrz3sIREnumS-lXy9MrkDq50nA5PEonQJ8snRUZI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);