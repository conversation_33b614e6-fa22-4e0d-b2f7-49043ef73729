
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 22% 12%; /* Dark Navy #1f2831 */
    --foreground: 0 0% 100%; /* White #FFFFFF */
    --card: 210 22% 12%; /* Dark Navy #1f2831 */
    --card-foreground: 0 0% 100%; /* White #FFFFFF */
    --popover: 210 22% 12%; /* Dark Navy #1f2831 */
    --popover-foreground: 0 0% 100%; /* White #FFFFFF */
    --primary: 0 0% 100%; /* White #FFFFFF */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 100%; /* White #FFFFFF */
    --secondary-foreground: 0 0% 100%; /* White */
    --muted: 210 28% 25%; /* Slate Gray #2E4053 */
    --muted-foreground: 0 0% 100%; /* White #FFFFFF */
    --accent: 0 0% 100%; /* White #FFFFFF */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 210 30% 40%; /* Trustworthy Blue #336699 */
    --chart-1: 51 92% 63%; /* Muted Gold #F1C40F */
    --chart-2: 174 60% 41%; /* Soft Green #16A085 */
    --chart-3: 210 30% 40%; /* Trustworthy Blue #336699 */
    --chart-4: 210 28% 25%; /* Slate Gray #2E4053 */
    --chart-5: 210 22% 16%; /* Deep Navy #1C2833 */
    --radius: 0.5rem;

    /* Custom The Otter Group Colors */
    --deep-navy: 210 22% 16%; /* #1C2833 */
    --slate-gray: 210 28% 25%; /* #2E4053 */
    --trustworthy-blue: 210 30% 40%; /* #336699 */
    --soft-green: 174 60% 41%; /* #16A085 */
    --muted-gold: 51 92% 63%; /* #F1C40F */
  }

  .dark {
    --background: 210 22% 12%; /* Dark Navy #1f2831 */
    --foreground: 0 0% 100%; /* White #FFFFFF */
    --card: 210 22% 12%; /* Dark Navy #1f2831 */
    --card-foreground: 0 0% 100%; /* White #FFFFFF */
    --popover: 210 22% 12%; /* Dark Navy #1f2831 */
    --popover-foreground: 0 0% 100%; /* White #FFFFFF */
    --primary: 0 0% 100%; /* White #FFFFFF */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 100%; /* White #FFFFFF */
    --secondary-foreground: 0 0% 100%; /* White */
    --muted: 210 28% 25%; /* Slate Gray #2E4053 */
    --muted-foreground: 0 0% 100%; /* White #FFFFFF */
    --accent: 0 0% 100%; /* White #FFFFFF */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 28% 35%; /* Lighter slate for borders */
    --input: 210 28% 35%; /* Lighter slate for inputs */
    --ring: 210 30% 40%; /* Trustworthy Blue #336699 */
    --chart-1: 51 92% 63%; /* Muted Gold #F1C40F */
    --chart-2: 174 60% 41%; /* Soft Green #16A085 */
    --chart-3: 210 30% 40%; /* Trustworthy Blue #336699 */
    --chart-4: 210 28% 25%; /* Slate Gray #2E4053 */
    --chart-5: 210 22% 16%; /* Deep Navy #1C2833 */

    /* Custom The Otter Group Colors */
    --deep-navy: 210 22% 16%; /* #1C2833 */
    --slate-gray: 210 28% 25%; /* #2E4053 */
    --trustworthy-blue: 210 30% 40%; /* #336699 */
    --soft-green: 174 60% 41%; /* #16A085 */
    --muted-gold: 51 92% 63%; /* #F1C40F */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }
}

/* Skeleton loading animations */
@keyframes gradient-pulse {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-gradient-pulse {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 25%,
    hsl(var(--muted) / 0.5) 50%,
    hsl(var(--muted)) 75%
  );
  background-size: 200% 100%;
  animation: gradient-pulse 1.5s ease-in-out infinite;
}

/* Hockey-themed background */
.hockey-background {
  position: relative;
  background-image: url('/hockey-bg.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.hockey-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(31, 40, 49, 0.92);
  pointer-events: none;
  z-index: 0;
}

.hockey-background > * {
  position: relative;
  z-index: 1;
}

/* Floating hockey elements */
.floating-hockey-element {
  position: fixed;
  width: 60px;
  height: 60px;
  background-image: url('/hockey-elements.svg');
  background-size: contain;
  background-repeat: no-repeat;
  pointer-events: none;
  opacity: 0.3;
  z-index: 0;
}

.floating-hockey-element:nth-child(1) {
  top: 20%;
  left: 10%;
  animation: float-1 12s ease-in-out infinite;
}

.floating-hockey-element:nth-child(2) {
  top: 60%;
  right: 15%;
  animation: float-2 15s ease-in-out infinite;
}

.floating-hockey-element:nth-child(3) {
  bottom: 30%;
  left: 20%;
  animation: float-3 18s ease-in-out infinite;
}

@keyframes float-1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-2 {
  0%, 100% { transform: translateX(0px) rotate(0deg); }
  50% { transform: translateX(-30px) rotate(-180deg); }
}

@keyframes float-3 {
  0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
  33% { transform: translate(15px, -15px) rotate(120deg); }
  66% { transform: translate(-15px, -10px) rotate(240deg); }
}

/* Custom fade-in animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* NHL Ticker Animation */
.ticker-container {
  overflow: hidden;
  white-space: nowrap;
}

.animate-scroll-ticker {
  display: inline-flex;
  animation: scroll-ticker 60s linear infinite;
}

@keyframes scroll-ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Pause animation on hover */
.ticker-container:hover .animate-scroll-ticker {
  animation-play-state: paused;
}

.paused {
  animation-play-state: paused !important;
}

/* Enhanced particle animations */
@keyframes float-particle {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.6;
  }
}

.animate-float-particle {
  animation: float-particle 4s ease-in-out infinite;
}

/* Slow spin animation */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Gradient pulse animation */
@keyframes gradient-pulse {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-pulse {
  background-size: 200% 200%;
  animation: gradient-pulse 3s ease infinite;
}

/* Interactive hover glow */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(51, 102, 153, 0.4);
  transform: translateY(-2px);
}

/* Comprehensive Glowing Card Effects */
/* Base glow effect for all cards */
.card-glow-base {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced glow effect on hover */
.card-glow-hover {
  box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 50px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.02);
}

/* Ring animation keyframes */
@keyframes ring-expand {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

/* Ring animation class */
.card-ring-animation {
  position: relative;
  overflow: visible;
}

.card-ring-animation::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: inherit;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.card-ring-animation:hover::before {
  animation: ring-expand 0.6s ease-out;
}

/* Subtle scaling and elevation */
.card-transform-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-transform-hover:hover {
  transform: translateY(-4px) scale(1.02);
}

/* Combined glow and ring effect */
.card-glow-ring {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
}

.card-glow-ring::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: inherit;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.card-glow-ring:hover {
  box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 50px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transform: translateY(-4px) scale(1.02);
}

.card-glow-ring:hover::before {
  animation: ring-expand 0.6s ease-out;
}

/* Header z-index management and layout shift prevention */
.header-sticky {
  /* Ensure header stays above all content */
  z-index: 1000;
  /* Prevent layout shifts with consistent height */
  height: 4rem; /* 64px */
  /* Smooth backdrop blur for better performance */
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Navigation menu z-index management - applies to all screen sizes */
.mobile-menu-overlay {
  z-index: 999;
}

.mobile-menu-panel {
  z-index: 1001;
}

/* Hamburger menu button styling for all screen sizes */
.hamburger-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure page content doesn't overlap with sticky header */
body {
  /* Add top padding equal to header height to prevent content overlap */
  padding-top: 0;
}

/* Smooth transitions for navigation menu - all screen sizes */
.navigation-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.navigation-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.navigation-menu-exit {
  opacity: 1;
  transform: translateY(0);
}

.navigation-menu-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}
