/**
 * @file contact-form.css
 * @description Enhanced styling for the Netlify contact form
 * 
 * This file provides additional styling for form elements including:
 * - Enhanced focus states and transitions
 * - Loading animations and states
 * - Error and success styling
 * - Responsive design improvements
 * - Accessibility enhancements
 */

/* Form Container Enhancements */
.contact-form-container {
  position: relative;
}

/* Required Field Indicator */
.required::after {
  content: " *";
  color: hsl(var(--destructive));
  font-weight: bold;
}

/* Enhanced Focus States */
.contact-form input:focus,
.contact-form textarea:focus,
.contact-form select:focus {
  transform: translateY(-1px);
  box-shadow: 
    0 0 0 2px hsl(var(--ring)),
    0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease-in-out;
}

/* Form Field Animations */
.form-field-enter {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State Styles */
.form-loading {
  position: relative;
  pointer-events: none;
}

.form-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: inherit;
  z-index: 1;
}

.form-loading .loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

/* Success State Animation */
.form-success {
  animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Error State Animation */
.form-error {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Enhanced Button States */
.contact-form button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.contact-form button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.contact-form button:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* White Button Specific Styles */
.contact-form button.bg-white {
  background-color: white !important;
  color: black !important;
  border: 2px solid #d1d5db;
}

.contact-form button.bg-white:hover {
  background-color: #f9fafb !important;
  border-color: #9ca3af;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.contact-form button.bg-white:focus {
  background-color: #f9fafb !important;
  border-color: #6b7280;
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.contact-form button.bg-white:disabled {
  background-color: #f3f4f6 !important;
  color: #6b7280 !important;
  border-color: #e5e7eb;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Button Loading State */
.contact-form button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.contact-form button .loading-dots {
  display: inline-flex;
  gap: 2px;
}

.contact-form button .loading-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: currentColor;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.contact-form button .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.contact-form button .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.contact-form button .loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Fieldset Enhancements */
.contact-form fieldset {
  position: relative;
  transition: all 0.3s ease;
}

.contact-form fieldset:focus-within {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 1px hsl(var(--ring));
}

.contact-form fieldset legend {
  position: relative;
  background: hsl(var(--background));
  padding: 0 12px;
}

/* Form Validation Styles */
.field-valid input,
.field-valid textarea,
.field-valid select {
  border-color: hsl(120, 60%, 50%);
}

.field-valid::after {
  content: "✓";
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: hsl(120, 60%, 50%);
  font-weight: bold;
  pointer-events: none;
}

.field-invalid input,
.field-invalid textarea,
.field-invalid select {
  border-color: hsl(var(--destructive));
  animation: fieldError 0.3s ease-in-out;
}

@keyframes fieldError {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
}

/* Character Counter */
.character-counter {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  text-align: right;
  margin-top: 4px;
}

.character-counter.warning {
  color: hsl(45, 100%, 50%);
}

.character-counter.error {
  color: hsl(var(--destructive));
}

/* Progress Indicator */
.form-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 2px;
  background: hsl(var(--primary));
  transition: width 0.3s ease;
  border-radius: 1px;
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    border-width: 2px;
  }
  
  .contact-form button {
    border-width: 2px;
    border-style: solid;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .contact-form *,
  .contact-form *::before,
  .contact-form *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .contact-form button {
    display: none;
  }
  
  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    border: 1px solid #000;
    background: white;
    color: black;
  }
}

/* Mobile Enhancements */
@media (max-width: 640px) {
  .contact-form fieldset {
    padding: 12px;
  }
  
  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Touch Device Enhancements */
@media (hover: none) and (pointer: coarse) {
  .contact-form button {
    min-height: 44px; /* Minimum touch target size */
  }
  
  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    min-height: 44px;
  }
}

/* Dark Mode Specific Enhancements */
@media (prefers-color-scheme: dark) {
  .contact-form input:focus,
  .contact-form textarea:focus,
  .contact-form select:focus {
    box-shadow: 
      0 0 0 2px hsl(var(--ring)),
      0 4px 12px rgba(255, 255, 255, 0.1);
  }
}

/* Form Submission States */
.form-submitting {
  opacity: 0.8;
  pointer-events: none;
}

.form-submitted.success {
  border-color: hsl(120, 60%, 50%);
  background: hsl(120, 60%, 95%);
}

.form-submitted.error {
  border-color: hsl(var(--destructive));
  background: hsl(var(--destructive) / 0.05);
}

/* Honeypot Field (should always be hidden) */
input[name="bot-field"],
input[name="botField"] {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
  tab-index: -1 !important;
}

/* Form Analytics and Tracking */
.form-analytics-pixel {
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}