import type { Plugin } from 'vite';

/**
 * Vite plugin to ensure <PERSON><PERSON> is properly loaded before any other code
 * This helps prevent "Cannot read properties of undefined (reading 'createContext')" errors
 */
export function ensureReactPlugin(): Plugin {
  return {
    name: 'ensure-react-plugin',
    enforce: 'pre',
    resolveId(id) {
      // Return null to let Vite handle the resolution
      return null;
    },
    transform(code, id) {
      // Only transform JavaScript/TypeScript files
      if (!id.match(/\.[jt]sx?$/)) {
        return null;
      }

      // Skip node_modules
      if (id.includes('node_modules')) {
        return null;
      }

      // Add React import to the top of every file if it uses JSX
      if (code.includes('jsx') || code.includes('tsx') || code.includes('<') && code.includes('>')) {
        // Check if the file already imports React
        if (!code.includes('import React') && !code.includes('import * as React')) {
          return `import * as React from 'react';\n${code}`;
        }
      }

      return null;
    }
  };
}