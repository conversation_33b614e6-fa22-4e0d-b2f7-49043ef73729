/**
 * @file Root Application Component
 * @description Orchestrates global providers and application routing
 * 
 * Architectural Responsibilities:
 * - Provider Pattern: Centralizes access to cross-cutting concerns
 * - Error Boundary: Implements global error handling
 * - Routing: Manages client-side navigation
 * 
 * Security Features:
 * - CSRF protection integration
 * - Authentication context management
 */

// Component for displaying traditional toast notifications.
import { Toaster } from "@/components/ui/toaster";
// Component for displaying Sonner-style (more modern) toast notifications.
import { Toaster as Sonner } from "@/components/ui/sonner";
// Provider for enabling tooltip functionality throughout the application.
import { TooltipProvider } from "@/components/ui/tooltip";
// Core library for client-side caching, server state management, and asynchronous operations.
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
// Components for enabling client-side navigation and defining application routes.
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { createLazyRoutes } from "./utils/code-splitting";
import { usePerformanceMonitor } from "./hooks/use-performance-monitor";
import { initializeCaching } from "./utils/caching-strategies";
import PerformanceMonitor from "./components/PerformanceMonitor";
import Header from "./components/Header";
import { useEffect } from "react";

// Create lazy-loaded routes with optimized loading
const routes = createLazyRoutes({
  Index: () => import("./pages/Index"),
  NotFound: () => import("./pages/NotFound")
});

// Configure QueryClient with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});



/**
 * Main application component establishing the provider hierarchy
 * @remarks
 * Provider Order Matters:
 * 1. QueryClientProvider: Must wrap data fetching context
 * 2. TooltipProvider: Requires React context access
 * 3. BrowserRouter: Should encapsulate route-dependent features
 * 
 * @returns {JSX.Element} Fully configured application root
 */
const App = () => {
  const { logMetrics } = usePerformanceMonitor();
  
  // Initialize caching and log performance metrics on app mount
  useEffect(() => {
    // Initialize caching strategies
    initializeCaching();
    
    const timer = setTimeout(() => {
      logMetrics();
    }, 2000); // Wait 2 seconds for initial load to complete
    
    return () => clearTimeout(timer);
  }, [logMetrics]);
  
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        {/* Notification System */}
        <Toaster />
        <Sonner />

        {/* Routing Configuration */}
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <Header />
          <Routes>
            {/* Core Application Routes with optimized lazy loading */}
            <Route path="/" element={<routes.Index />} />

            {/* Error Handling */}
            <Route path="*" element={<routes.NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
      <PerformanceMonitor />
    </QueryClientProvider>
  );
};

export default App;
