import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * @file CSS Utility Functions
 * @description Provides class name merging utilities for Tailwind CSS integration
 * 
 * Primary Function:
 * - cn(): Merges multiple class names while resolving Tailwind conflicts
 * 
 * @example
 * cn('px-2', 'py-4', 'bg-red-500') => 'px-2 py-4 bg-red-500'
 * 
 * @param {...ClassValue[]} inputs - Array of Tailwind class values
 * @returns {string} Optimized, merged class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
