/**
 * Code splitting utilities for optimized bundle loading
 * Provides lazy loading with error boundaries and loading states
 */

import { lazy, Suspense, ComponentType, ReactNode, useState, useCallback, useEffect, Component } from 'react';
import { Skeleton, CardSkeleton, TextSkeleton } from '@/components/ui/SkeletonLoader';

/**
 * Simple Error Boundary component
 */
class ErrorBoundary extends Component<
  { children: ReactNode; fallback: ComponentType<{ error: Error; resetErrorBoundary: () => void }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  resetErrorBoundary = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback;
      return <FallbackComponent error={this.state.error} resetErrorBoundary={this.resetErrorBoundary} />;
    }

    return this.props.children;
  }
}

/**
 * Loading component for lazy-loaded components
 */
const LoadingSpinner = ({ message = 'Loading...' }: { message?: string }) => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  </div>
);

/**
 * Skeleton loading component for better perceived performance
 */
const SkeletonLoader = ({ 
  type = 'default', 
  props = {} 
}: { 
  type?: 'default' | 'card' | 'text' | 'custom';
  props?: any;
}) => {
  const { lines = 3, height = '200px', className = '' } = props;
  
  switch (type) {
    case 'card':
      return (
        <div className={`min-h-[200px] w-full ${className}`}>
          <CardSkeleton />
        </div>
      );
    case 'text':
      return (
        <div className={`min-h-[100px] w-full p-4 ${className}`}>
          <TextSkeleton lines={lines} />
        </div>
      );
    case 'custom':
      return (
        <div className={`w-full ${className}`} style={{ minHeight: height }}>
          <Skeleton className="h-full w-full" />
        </div>
      );
    default:
      return (
        <div className={`min-h-[200px] w-full ${className}`}>
          <Skeleton className="h-full w-full" />
        </div>
      );
  }
};

/**
 * Error fallback component for failed lazy loads
 */
const ErrorFallback = ({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="text-center space-y-4 p-6 border border-destructive/20 rounded-lg bg-destructive/5">
      <h3 className="text-lg font-semibold text-destructive">Something went wrong</h3>
      <p className="text-sm text-muted-foreground">
        {error.message || 'Failed to load component'}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
      >
        Try again
      </button>
    </div>
  </div>
);

/**
 * Options for lazy loading components
 */
interface LazyLoadOptions {
  loadingMessage?: string;
  fallback?: ReactNode;
  retryAttempts?: number;
  preload?: boolean;
  skeleton?: 'default' | 'card' | 'text' | 'custom';
  skeletonProps?: {
    lines?: number;
    height?: string;
    className?: string;
  };
}

/**
 * Creates a lazy-loaded component with error boundary and loading state
 * @param importFn - Function that returns a dynamic import promise
 * @param options - Configuration options for lazy loading
 * @returns Wrapped lazy component with error handling
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
) {
  const {
    loadingMessage = 'Loading component...',
    fallback,
    retryAttempts = 3,
    preload = false,
    skeleton = 'default',
    skeletonProps = {}
  } = options;

  // Create the lazy component
  const LazyComponent = lazy(importFn);

  // Preload the component if requested
  if (preload) {
    importFn().catch(() => {
      // Silently handle preload failures
    });
  }

  // Return wrapped component with error boundary and suspense
  const WrappedComponent = (props: any) => {
    let retryCount = 0;

    const handleRetry = () => {
      if (retryCount < retryAttempts) {
        retryCount++;
        window.location.reload();
      }
    };

    return (
      <ErrorBoundary fallback={ErrorFallback}>
        <Suspense fallback={fallback || <SkeletonLoader type={skeleton} props={skeletonProps} />}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };

  // Add display name for debugging
  WrappedComponent.displayName = `LazyWrapper(Component)`;

  return WrappedComponent;
}

/**
 * Creates route-based lazy components for React Router
 * @param routes - Object mapping route paths to import functions
 * @returns Object with lazy-loaded route components
 */
export function createLazyRoutes<T extends Record<string, () => Promise<{ default: ComponentType<any> }>>>(
  routes: T
): Record<keyof T, ComponentType<any>> {
  const lazyRoutes = {} as Record<keyof T, ComponentType<any>>;

  Object.entries(routes).forEach(([path, importFn]) => {
    lazyRoutes[path as keyof T] = createLazyComponent(importFn, {
      loadingMessage: `Loading ${path} page...`,
      preload: false
    });
  });

  return lazyRoutes;
}

/**
 * Preloads a lazy component for better UX
 * @param importFn - Function that returns a dynamic import promise
 * @returns Promise that resolves when component is loaded
 */
export function preloadComponent(
  importFn: () => Promise<{ default: ComponentType<any> }>
): Promise<void> {
  return importFn()
    .then(() => {
      console.log('Component preloaded successfully');
    })
    .catch((error) => {
      console.warn('Failed to preload component:', error);
    });
}

/**
 * Hook for preloading components on user interaction
 * @param importFn - Function that returns a dynamic import promise
 * @returns Object with preload function and loading state
 */
export function useComponentPreloader(
  importFn: () => Promise<{ default: ComponentType<any> }>
) {
  const [isPreloading, setIsPreloading] = useState(false);
  const [isPreloaded, setIsPreloaded] = useState(false);

  const preload = useCallback(async () => {
    if (isPreloaded || isPreloading) return;

    setIsPreloading(true);
    try {
      await importFn();
      setIsPreloaded(true);
    } catch (error) {
      console.warn('Preload failed:', error);
    } finally {
      setIsPreloading(false);
    }
  }, [importFn, isPreloaded, isPreloading]);

  return { preload, isPreloading, isPreloaded };
}

/**
 * Bundle analysis utilities
 */
export const bundleAnalysis = {
  /**
   * Logs bundle chunk information for debugging
   */
  logChunkInfo: () => {
    if (typeof window !== 'undefined' && window.performance) {
      const entries = performance.getEntriesByType('navigation');
      console.group('Bundle Analysis');
      console.log('Navigation entries:', entries);
      console.log('Resource timing:', performance.getEntriesByType('resource'));
      console.groupEnd();
    }
  },

  /**
   * Measures the size of dynamically imported chunks
   */
  measureChunkSize: async (importFn: () => Promise<any>) => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    await importFn();
    
    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    return {
      loadTime: endTime - startTime,
      memoryIncrease: endMemory - startMemory
    };
  }
};

/**
 * Performance monitoring for lazy-loaded components
 */
export function withPerformanceMonitoring<P extends object>(
  Component: ComponentType<P>,
  componentName: string
) {
  return function MonitoredComponent(props: P) {
    useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        if (renderTime > 100) { // Log slow renders
          console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`);
        }
      };
    }, []);

    return <Component {...props} />;
  };
}