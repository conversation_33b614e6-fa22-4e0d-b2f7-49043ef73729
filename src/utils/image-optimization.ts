/**
 * Image optimization utilities for better performance
 * Provides functions for responsive images, format detection, and lazy loading
 */

/**
 * Generates responsive image sources with different formats and sizes
 * @param basePath - Base path of the image without extension
 * @param extension - Original image extension
 * @param sizes - Array of widths for responsive images
 * @returns Object with srcSet and sizes for responsive images
 */
export function generateResponsiveImageSources(
  basePath: string,
  extension: string,
  sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
) {
  const webpSrcSet = sizes
    .map(size => `${basePath}-${size}w.webp ${size}w`)
    .join(', ');
  
  const fallbackSrcSet = sizes
    .map(size => `${basePath}-${size}w.${extension} ${size}w`)
    .join(', ');
  
  return {
    webpSrcSet,
    fallbackSrcSet,
    sizes: '(max-width: 320px) 320px, (max-width: 640px) 640px, (max-width: 768px) 768px, (max-width: 1024px) 1024px, (max-width: 1280px) 1280px, 1920px'
  };
}

/**
 * Checks if WebP format is supported by the browser
 * @returns Promise that resolves to boolean indicating WebP support
 */
export function checkWebPSupport(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Preloads critical images for better performance
 * @param imageSources - Array of image sources to preload
 * @param priority - Loading priority (high, low, auto)
 */
export function preloadCriticalImages(
  imageSources: string[],
  priority: 'high' | 'low' | 'auto' = 'high'
) {
  imageSources.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    if (priority !== 'auto') {
      link.setAttribute('fetchpriority', priority);
    }
    document.head.appendChild(link);
  });
}

/**
 * Optimizes image loading based on connection speed
 * @returns Recommended image quality based on network conditions
 */
export function getOptimalImageQuality(): 'low' | 'medium' | 'high' {
  // @ts-ignore - navigator.connection is experimental
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  
  if (!connection) return 'medium';
  
  const { effectiveType, downlink } = connection;
  
  if (effectiveType === '4g' && downlink > 2) return 'high';
  if (effectiveType === '3g' || (effectiveType === '4g' && downlink <= 2)) return 'medium';
  return 'low';
}

/**
 * Creates an optimized image URL based on quality settings
 * @param originalUrl - Original image URL
 * @param quality - Desired quality level
 * @returns Optimized image URL
 */
export function createOptimizedImageUrl(
  originalUrl: string,
  quality: 'low' | 'medium' | 'high' = 'medium'
): string {
  const qualityMap = {
    low: 60,
    medium: 80,
    high: 95
  };
  
  // For external URLs (like images from CDNs), append quality parameters
  if (originalUrl.startsWith('http')) {
    const url = new URL(originalUrl);
    url.searchParams.set('q', qualityMap[quality].toString());
    return url.toString();
  }
  
  return originalUrl;
}

/**
 * Intersection Observer options for lazy loading
 */
export const lazyLoadOptions: IntersectionObserverInit = {
  root: null,
  rootMargin: '50px',
  threshold: 0.1
};

/**
 * Image loading states for better UX
 */
export enum ImageLoadingState {
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

/**
 * Performance metrics for image loading
 */
export interface ImagePerformanceMetrics {
  loadTime: number;
  fileSize?: number;
  format: string;
  dimensions: { width: number; height: number };
}

/**
 * Measures image loading performance
 * @param imageElement - Image element to measure
 * @returns Performance metrics object
 */
export function measureImagePerformance(
  imageElement: HTMLImageElement
): ImagePerformanceMetrics {
  const loadTime = performance.now();
  
  return {
    loadTime,
    format: imageElement.src.split('.').pop() || 'unknown',
    dimensions: {
      width: imageElement.naturalWidth,
      height: imageElement.naturalHeight
    }
  };
}