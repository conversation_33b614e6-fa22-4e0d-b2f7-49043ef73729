/**
 * Comprehensive caching strategies for optimal performance
 * Includes service worker registration, cache management, and storage utilities
 */

/**
 * Cache configuration options
 */
interface CacheConfig {
  name: string;
  version: string;
  maxAge: number; // in milliseconds
  maxEntries?: number;
}

/**
 * Default cache configurations for different resource types
 */
export const cacheConfigs: Record<string, CacheConfig> = {
  static: {
    name: 'static-assets',
    version: 'v1',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 100
  },
  api: {
    name: 'api-responses',
    version: 'v1',
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxEntries: 50
  },
  images: {
    name: 'image-cache',
    version: 'v1',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxEntries: 200
  },
  fonts: {
    name: 'font-cache',
    version: 'v1',
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    maxEntries: 20
  }
};

/**
 * Browser storage utilities with expiration support
 */
export class StorageManager {
  private static instance: StorageManager;
  
  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * Sets an item in localStorage with expiration
   * @param key - Storage key
   * @param value - Value to store
   * @param ttl - Time to live in milliseconds
   */
  setItem(key: string, value: any, ttl?: number): void {
    try {
      const item = {
        value,
        timestamp: Date.now(),
        ttl: ttl || 24 * 60 * 60 * 1000 // Default 24 hours
      };
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set localStorage item:', error);
    }
  }

  /**
   * Gets an item from localStorage, checking expiration
   * @param key - Storage key
   * @returns Stored value or null if expired/not found
   */
  getItem(key: string): any {
    try {
      const itemStr = localStorage.getItem(key);
      if (!itemStr) return null;

      const item = JSON.parse(itemStr);
      const now = Date.now();

      if (now - item.timestamp > item.ttl) {
        localStorage.removeItem(key);
        return null;
      }

      return item.value;
    } catch (error) {
      console.warn('Failed to get localStorage item:', error);
      return null;
    }
  }

  /**
   * Removes an item from localStorage
   * @param key - Storage key
   */
  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to remove localStorage item:', error);
    }
  }

  /**
   * Clears expired items from localStorage
   */
  clearExpired(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        this.getItem(key); // This will remove expired items
      });
    } catch (error) {
      console.warn('Failed to clear expired items:', error);
    }
  }

  /**
   * Gets storage usage information
   * @returns Object with storage usage stats
   */
  getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0;
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
      
      const available = 5 * 1024 * 1024; // Approximate 5MB limit
      const percentage = (used / available) * 100;
      
      return { used, available, percentage };
    } catch (error) {
      console.warn('Failed to get storage info:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }
}

/**
 * Memory cache for runtime data
 */
export class MemoryCache {
  private cache = new Map<string, { value: any; timestamp: number; ttl: number }>();
  private static instance: MemoryCache;
  
  static getInstance(): MemoryCache {
    if (!MemoryCache.instance) {
      MemoryCache.instance = new MemoryCache();
    }
    return MemoryCache.instance;
  }

  /**
   * Sets a value in memory cache
   * @param key - Cache key
   * @param value - Value to cache
   * @param ttl - Time to live in milliseconds
   */
  set(key: string, value: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Gets a value from memory cache
   * @param key - Cache key
   * @returns Cached value or null if expired/not found
   */
  get(key: string): any {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * Removes a value from memory cache
   * @param key - Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clears all expired entries
   */
  clearExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Gets cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

/**
 * Service Worker registration and management
 */
export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager;
  
  static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager();
    }
    return ServiceWorkerManager.instance;
  }

  /**
   * Registers a service worker
   * @param swPath - Path to service worker file
   * @returns Promise that resolves when registration is complete
   */
  async register(swPath: string = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Workers not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register(swPath);
      console.log('Service Worker registered:', registration);
      
      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New service worker available');
              // Optionally notify user about update
            }
          });
        }
      });

      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  }

  /**
   * Unregisters all service workers
   */
  async unregisterAll(): Promise<void> {
    if (!('serviceWorker' in navigator)) return;

    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      await Promise.all(registrations.map(reg => reg.unregister()));
      console.log('All service workers unregistered');
    } catch (error) {
      console.error('Failed to unregister service workers:', error);
    }
  }

  /**
   * Checks if service worker is active
   */
  isActive(): boolean {
    return !!navigator.serviceWorker?.controller;
  }
}

/**
 * HTTP cache utilities
 */
export const httpCache = {
  /**
   * Creates cache headers for different resource types
   * @param type - Resource type
   * @returns Cache headers object
   */
  getHeaders(type: 'static' | 'api' | 'images' | 'fonts'): Record<string, string> {
    const config = cacheConfigs[type];
    const maxAge = Math.floor(config.maxAge / 1000); // Convert to seconds

    switch (type) {
      case 'static':
        return {
          'Cache-Control': `public, max-age=${maxAge}, immutable`,
          'ETag': `"${config.version}"`,
        };
      case 'api':
        return {
          'Cache-Control': `public, max-age=${maxAge}, must-revalidate`,
        };
      case 'images':
        return {
          'Cache-Control': `public, max-age=${maxAge}`,
          'Vary': 'Accept',
        };
      case 'fonts':
        return {
          'Cache-Control': `public, max-age=${maxAge}, immutable`,
          'Access-Control-Allow-Origin': '*',
        };
      default:
        return {
          'Cache-Control': 'no-cache',
        };
    }
  },

  /**
   * Preloads critical resources
   * @param resources - Array of resource URLs
   * @param type - Resource type for cache headers
   */
  preloadResources(resources: string[], type: 'script' | 'style' | 'image' | 'font' = 'script'): void {
    resources.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      link.as = type;
      
      if (type === 'font') {
        link.crossOrigin = 'anonymous';
      }
      
      document.head.appendChild(link);
    });
  }
};

/**
 * Cache performance monitoring
 */
export const cacheMonitor = {
  /**
   * Measures cache hit ratio
   */
  measureHitRatio(): { hits: number; misses: number; ratio: number } {
    const storage = StorageManager.getInstance();
    const hits = parseInt(storage.getItem('cache_hits') || '0');
    const misses = parseInt(storage.getItem('cache_misses') || '0');
    const total = hits + misses;
    const ratio = total > 0 ? hits / total : 0;
    
    return { hits, misses, ratio };
  },

  /**
   * Records a cache hit
   */
  recordHit(): void {
    const storage = StorageManager.getInstance();
    const hits = parseInt(storage.getItem('cache_hits') || '0') + 1;
    storage.setItem('cache_hits', hits.toString());
  },

  /**
   * Records a cache miss
   */
  recordMiss(): void {
    const storage = StorageManager.getInstance();
    const misses = parseInt(storage.getItem('cache_misses') || '0') + 1;
    storage.setItem('cache_misses', misses.toString());
  },

  /**
   * Gets comprehensive cache statistics
   */
  getStats(): {
    hitRatio: { hits: number; misses: number; ratio: number };
    storage: { used: number; available: number; percentage: number };
    memory: { size: number; keys: string[] };
  } {
    const storage = StorageManager.getInstance();
    const memory = MemoryCache.getInstance();
    
    return {
      hitRatio: this.measureHitRatio(),
      storage: storage.getStorageInfo(),
      memory: memory.getStats()
    };
  }
};

/**
 * Initialize caching system
 */
export function initializeCaching(): void {
  // Clear expired items on startup
  const storage = StorageManager.getInstance();
  const memory = MemoryCache.getInstance();
  
  storage.clearExpired();
  memory.clearExpired();
  
  // Set up periodic cleanup
  setInterval(() => {
    storage.clearExpired();
    memory.clearExpired();
  }, 60 * 60 * 1000); // Every hour
  
  // Register service worker if available
  const swManager = ServiceWorkerManager.getInstance();
  swManager.register().catch(console.error);
  
  console.log('Caching system initialized');
}