/**
 * @file use-performance-monitor.ts
 * @description Custom hook for monitoring Core Web Vitals and performance metrics
 * 
 * Metrics Tracked:
 * - First Contentful Paint (FCP)
 * - Largest Contentful Paint (LCP)
 * - First Input Delay (FID)
 * - Cumulative Layout Shift (CLS)
 * - Time to First Byte (TTFB)
 * - Bundle size and load times
 */

import { useEffect, useCallback, useState } from 'react';

interface PerformanceMetrics {
  fcp?: number;
  lcp?: number;
  fid?: number;
  cls?: number;
  ttfb?: number;
  bundleSize?: number;
  loadTime?: number;
}

interface PerformanceThresholds {
  fcp: { good: number; poor: number };
  lcp: { good: number; poor: number };
  fid: { good: number; poor: number };
  cls: { good: number; poor: number };
  ttfb: { good: number; poor: number };
}

/**
 * Performance thresholds based on Core Web Vitals standards
 */
const PERFORMANCE_THRESHOLDS: PerformanceThresholds = {
  fcp: { good: 1800, poor: 3000 }, // milliseconds
  lcp: { good: 2500, poor: 4000 }, // milliseconds
  fid: { good: 100, poor: 300 },   // milliseconds
  cls: { good: 0.1, poor: 0.25 },  // score
  ttfb: { good: 800, poor: 1800 }  // milliseconds
};

/**
 * Custom hook for performance monitoring
 * @returns Performance metrics and monitoring functions
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isSupported, setIsSupported] = useState(false);

  /**
   * Check if Performance Observer API is supported
   */
  const checkSupport = useCallback(() => {
    return typeof window !== 'undefined' && 
           'PerformanceObserver' in window && 
           'performance' in window;
  }, []);

  /**
   * Get performance score based on thresholds
   * @param value - Metric value
   * @param thresholds - Good/poor thresholds
   * @returns Performance score (good/needs-improvement/poor)
   */
  const getPerformanceScore = useCallback((value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.poor) return 'needs-improvement';
    return 'poor';
  }, []);

  /**
   * Measure First Contentful Paint (FCP)
   */
  const measureFCP = useCallback(() => {
    if (!checkSupport()) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      
      if (fcpEntry) {
        setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }));
        observer.disconnect();
      }
    });

    observer.observe({ entryTypes: ['paint'] });
  }, [checkSupport]);

  /**
   * Measure Largest Contentful Paint (LCP)
   */
  const measureLCP = useCallback(() => {
    if (!checkSupport()) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (lastEntry) {
        setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });

    // Stop observing after page load
    setTimeout(() => observer.disconnect(), 10000);
  }, [checkSupport]);

  /**
   * Measure First Input Delay (FID)
   */
  const measureFID = useCallback(() => {
    if (!checkSupport()) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fidEntry = entries[0] as PerformanceEventTiming;
      
      if (fidEntry && 'processingStart' in fidEntry) {
        const fid = fidEntry.processingStart - fidEntry.startTime;
        setMetrics(prev => ({ ...prev, fid }));
        observer.disconnect();
      }
    });

    observer.observe({ entryTypes: ['first-input'] });
  }, [checkSupport]);

  /**
   * Measure Cumulative Layout Shift (CLS)
   */
  const measureCLS = useCallback(() => {
    if (!checkSupport()) return;

    let clsValue = 0;
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      
      setMetrics(prev => ({ ...prev, cls: clsValue }));
    });

    observer.observe({ entryTypes: ['layout-shift'] });

    // Stop observing after page load
    setTimeout(() => observer.disconnect(), 10000);
  }, [checkSupport]);

  /**
   * Measure Time to First Byte (TTFB)
   */
  const measureTTFB = useCallback(() => {
    if (!checkSupport()) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const navigationEntry = entries[0] as PerformanceNavigationTiming;
      
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        setMetrics(prev => ({ ...prev, ttfb }));
        observer.disconnect();
      }
    });

    observer.observe({ entryTypes: ['navigation'] });
  }, [checkSupport]);

  /**
   * Measure bundle size and load time
   */
  const measureBundleMetrics = useCallback(() => {
    if (!checkSupport()) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      let totalSize = 0;
      let loadTime = 0;
      
      entries.forEach((entry: any) => {
        if (entry.name.includes('.js') || entry.name.includes('.css')) {
          totalSize += entry.transferSize || 0;
          loadTime = Math.max(loadTime, entry.responseEnd - entry.startTime);
        }
      });
      
      setMetrics(prev => ({ 
        ...prev, 
        bundleSize: totalSize,
        loadTime 
      }));
    });

    observer.observe({ entryTypes: ['resource'] });
  }, [checkSupport]);

  /**
   * Get performance report with scores
   */
  const getPerformanceReport = useCallback(() => {
    const report = {
      metrics,
      scores: {
        fcp: metrics.fcp ? getPerformanceScore(metrics.fcp, PERFORMANCE_THRESHOLDS.fcp) : null,
        lcp: metrics.lcp ? getPerformanceScore(metrics.lcp, PERFORMANCE_THRESHOLDS.lcp) : null,
        fid: metrics.fid ? getPerformanceScore(metrics.fid, PERFORMANCE_THRESHOLDS.fid) : null,
        cls: metrics.cls ? getPerformanceScore(metrics.cls, PERFORMANCE_THRESHOLDS.cls) : null,
        ttfb: metrics.ttfb ? getPerformanceScore(metrics.ttfb, PERFORMANCE_THRESHOLDS.ttfb) : null,
      },
      thresholds: PERFORMANCE_THRESHOLDS
    };
    
    return report;
  }, [metrics, getPerformanceScore]);

  /**
   * Log performance metrics to console (development only)
   */
  const logMetrics = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      const report = getPerformanceReport();
      console.group('🚀 Performance Metrics');
      console.table(report.metrics);
      console.table(report.scores);
      console.groupEnd();
    }
  }, [getPerformanceReport]);

  // Initialize performance monitoring
  useEffect(() => {
    const supported = checkSupport();
    setIsSupported(supported);
    
    if (supported) {
      measureFCP();
      measureLCP();
      measureFID();
      measureCLS();
      measureTTFB();
      measureBundleMetrics();
      
      // Log metrics after initial load
      setTimeout(logMetrics, 5000);
    }
  }, [checkSupport, measureFCP, measureLCP, measureFID, measureCLS, measureTTFB, measureBundleMetrics, logMetrics]);

  return {
    metrics,
    isSupported,
    getPerformanceReport,
    logMetrics,
    thresholds: PERFORMANCE_THRESHOLDS
  };
};