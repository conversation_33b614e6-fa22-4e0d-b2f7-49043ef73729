import * as React from "react";

const MOBILE_BREAKPOINT = 768;

/**
 * @file useIsMobile Hook
 * @description Detects mobile viewport status and provides responsive updates
 * 
 * Implementation Details:
 * - Uses window.matchMedia API for efficient detection
 * - Updates state on viewport resize (with debounced updates)
 * - Default breakpoint: 768px (matches Tailwind's md: breakpoint)
 * 
 * @returns {boolean} Current mobile detection status
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(
    undefined,
  );

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };
    mql.addEventListener("change", onChange);
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    return () => mql.removeEventListener("change", onChange);
  }, []);

  return !!isMobile;
}
