import { useEffect, useState, RefObject } from 'react';

interface UseIntersectionObserverProps {
  root?: Element | null;
  rootMargin?: string;
  threshold?: number | number[];
  once?: boolean;
}

/**
 * Custom hook for detecting when an element enters the viewport
 * Useful for implementing lazy loading of components or images
 */
export function useIntersectionObserver(
  elementRef: RefObject<Element>,
  {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    once = true,
  }: UseIntersectionObserverProps = {}
): boolean {
  const [isIntersecting, setIsIntersecting] = useState<boolean>(false);

  useEffect(() => {
    const element = elementRef?.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        
        // If once is true and the element is intersecting, unobserve it
        if (once && entry.isIntersecting) {
          observer.unobserve(element);
        }
      },
      { root, rootMargin, threshold }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, root, rootMargin, threshold, once]);

  return isIntersecting;
}