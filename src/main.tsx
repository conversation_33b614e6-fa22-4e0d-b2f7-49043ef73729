/**
 * @file main.tsx
 * This is the main entry point for The Otter Group application.
 * It sets up the React root and renders the main App component.
 */

// Explicitly import React to ensure it's available globally
import React from 'react';
// Imports React's ability to create a root for rendering the application.
import { createRoot } from "react-dom/client";
import { StrictMode } from 'react';
// Imports the main App component.
import App from "./App.tsx";
// Imports global styles for the application.
import "./index.css";
// Import the 21st Extension Toolbar for development tools
import { initToolbar } from '@21st-extension/toolbar';

// Make React available globally to prevent "React is not defined" errors
if (typeof window !== 'undefined') {
  window.React = React;
}

/**
 * Sets up the 21st Extension Toolbar for development mode
 * This provides enhanced development tools and debugging capabilities
 */
function setupStagewise() {
  // Only initialize once and only in development mode
  if (process.env.NODE_ENV === 'development') {
    // Define toolbar configuration with empty plugins array
    const stagewiseConfig = {
      plugins: [],
    };
    
    // Initialize the toolbar with the configuration
    initToolbar(stagewiseConfig);
  }
}

// Initialize the development toolbar
setupStagewise();

// Creates a React root attached to the HTML element with the ID "root"
// and then renders the App component into this root.
// The "!" after getElementById("root") is a non-null assertion operator,
// telling TypeScript that we are sure the element exists.
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
