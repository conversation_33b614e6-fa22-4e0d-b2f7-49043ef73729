# Environment Setup

## Google Maps API Configuration

This project uses Google Maps to display location information. To set up the Google Maps integration:

### 1. Environment Variables

Create a `.env` file in the project root with the following variable:

```
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

### 2. Getting a Google Maps API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Maps JavaScript API
4. Create credentials (API Key)
5. Restrict the API key to your domain for security

### 3. Security Notes

- The `.env` file is already added to `.gitignore` to prevent committing sensitive data
- Use the `.env.example` file as a template for required environment variables
- For production deployments, set environment variables through your hosting platform

### 4. Development Setup

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your actual Google Maps API key

3. Restart the development server:
   ```bash
   npm run dev
   ```

### 5. Troubleshooting

- If the map doesn't load, check the browser console for API key errors
- Ensure your API key has the Maps JavaScript API enabled
- Verify the API key is correctly set in the `.env` file
- Make sure to restart the development server after changing environment variables